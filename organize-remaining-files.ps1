# Script para organizar arquivos JavaScript restantes
# Logyc Contabilidade - Organização Final

Write-Host "🧹 Organizando arquivos JavaScript restantes..." -ForegroundColor Green

# Mapeamento de arquivos JavaScript para suas pastas corretas
$jsFiles = @{
    # Core scripts
    "analytics.js" = "assets\js\core\analytics.js"
    "performance.js" = "assets\js\core\performance.js"
    "accessibility.js" = "assets\js\core\accessibility.js"
    "validation.js" = "assets\js\core\validation.js"
    "loading.js" = "assets\js\core\loading.js"
    "pwa-manager.js" = "assets\js\core\pwa-manager.js"
    
    # Components
    "chat.js" = "assets\js\components\chat.js"
    "faq.js" = "assets\js\components\faq.js"
    "testimonials.js" = "assets\js\components\testimonials.js"
    "notifications.js" = "assets\js\components\notifications.js"
    "modern-ui.js" = "assets\js\components\modern-ui.js"
    
    # Pages
    "admin-dashboard.js" = "assets\js\pages\admin-dashboard.js"
    "blog.js" = "assets\js\pages\blog.js"
    "help-form.js" = "assets\js\pages\help-form.js"
    "switch-form.js" = "assets\js\pages\switch-form.js"
    
    # Advanced features
    "advanced-analytics.js" = "assets\js\vendor\advanced-analytics.js"
    "ab-testing.js" = "assets\js\vendor\ab-testing.js"
    "conversion-optimizer.js" = "assets\js\vendor\conversion-optimizer.js"
    "marketing-automation.js" = "assets\js\vendor\marketing-automation.js"
    "ai-assistant.js" = "assets\js\vendor\ai-assistant.js"
    
    # Main script (rename old one)
    "script.js" = "assets\js\main-legacy.js"
}

# Mover arquivos JavaScript
Write-Host "`n📜 Movendo arquivos JavaScript..." -ForegroundColor Cyan

foreach ($file in $jsFiles.Keys) {
    if (Test-Path $file) {
        $destination = $jsFiles[$file]
        $destinationDir = Split-Path $destination -Parent
        
        # Criar diretório se não existir
        if (!(Test-Path $destinationDir)) {
            New-Item -ItemType Directory -Path $destinationDir -Force | Out-Null
        }
        
        Move-Item $file $destination -Force
        Write-Host "✅ $file -> $destination" -ForegroundColor Green
    } else {
        Write-Host "⚠️  $file não encontrado" -ForegroundColor Yellow
    }
}

# Mover arquivo CSS restante
Write-Host "`n📁 Movendo arquivos CSS restantes..." -ForegroundColor Cyan

if (Test-Path "forms.css") {
    Move-Item "forms.css" "assets\css\forms.css" -Force
    Write-Host "✅ forms.css -> assets/css/forms.css" -ForegroundColor Green
}

# Mover logo restante
Write-Host "`n🖼️ Movendo imagens restantes..." -ForegroundColor Cyan

if (Test-Path "logo.png") {
    Move-Item "logo.png" "assets\images\logos\logo-main.png" -Force
    Write-Host "✅ logo.png -> assets/images/logos/logo-main.png" -ForegroundColor Green
}

# Mover documentação para docs
Write-Host "`n📚 Organizando documentação..." -ForegroundColor Cyan

$docFiles = @{
    "checklist.md" = "docs\features\checklist.md"
    "migrate-structure.md" = "docs\setup\migrate-structure.md"
    "ADVANCED-SYSTEMS.md" = "docs\features\ADVANCED-SYSTEMS.md"
    "CONFIGURACAO.md" = "docs\setup\CONFIGURACAO.md"
    "FINAL-DOCUMENTATION.md" = "docs\FINAL-DOCUMENTATION.md"
    "FUNCIONALIDADES.md" = "docs\features\FUNCIONALIDADES.md"
    "PREMIUM-FEATURES.md" = "docs\features\PREMIUM-FEATURES.md"
}

foreach ($file in $docFiles.Keys) {
    if (Test-Path $file) {
        $destination = $docFiles[$file]
        $destinationDir = Split-Path $destination -Parent
        
        # Criar diretório se não existir
        if (!(Test-Path $destinationDir)) {
            New-Item -ItemType Directory -Path $destinationDir -Force | Out-Null
        }
        
        Move-Item $file $destination -Force
        Write-Host "✅ $file -> $destination" -ForegroundColor Green
    }
}

# Atualizar imports no main.css
Write-Host "`n🎨 Atualizando imports CSS..." -ForegroundColor Cyan

if (Test-Path "assets\css\main.css") {
    $cssContent = Get-Content "assets\css\main.css" -Raw
    
    # Adicionar import do forms.css se não existir
    if ($cssContent -notmatch "@import.*forms\.css") {
        $newImport = "@import url('forms.css');"
        $cssContent = $cssContent -replace "(@import url\('components\.css'\);)", "$1`n$newImport"
        Set-Content "assets\css\main.css" $cssContent
        Write-Host "✅ Adicionado import forms.css ao main.css" -ForegroundColor Green
    }
}

Write-Host "`n🎉 Organização concluída!" -ForegroundColor Green
Write-Host "`n📊 Estrutura final:" -ForegroundColor Cyan
Write-Host "   assets/css/ - Todos os estilos" -ForegroundColor White
Write-Host "   assets/js/core/ - Scripts principais" -ForegroundColor White
Write-Host "   assets/js/components/ - Componentes" -ForegroundColor White
Write-Host "   assets/js/pages/ - Scripts de páginas" -ForegroundColor White
Write-Host "   assets/js/vendor/ - Bibliotecas avançadas" -ForegroundColor White
Write-Host "   assets/images/ - Todas as imagens" -ForegroundColor White
Write-Host "   docs/ - Documentação organizada" -ForegroundColor White

Write-Host "`n📋 Arquivos na raiz agora:" -ForegroundColor Cyan
Get-ChildItem -File | Where-Object { $_.Extension -eq ".html" -or $_.Extension -eq ".md" -or $_.Extension -eq ".json" -or $_.Extension -eq ".txt" -or $_.Extension -eq ".xml" } | ForEach-Object {
    Write-Host "   $($_.Name)" -ForegroundColor White
}

pause
