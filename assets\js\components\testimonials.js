// Sistema de Depoimentos - Logyc Contabilidade
class TestimonialsSystem {
    constructor() {
        this.testimonials = this.getTestimonialsData();
        this.currentIndex = 0;
        this.autoPlayInterval = null;
        this.init();
    }

    init() {
        this.createTestimonialsSection();
        this.renderTestimonials();
        this.setupEventListeners();
        this.startAutoPlay();
    }

    getTestimonialsData() {
        return [
            {
                id: 1,
                name: '<PERSON>',
                company: 'Silva & Associados LTDA',
                role: 'Empresária',
                text: 'A Logyc transformou a gestão da minha empresa. Profissionais competentes, atendimento excepcional e sempre disponíveis para esclarecer dúvidas.',
                rating: 5,
                image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face&auto=format',
                date: '2024-01-15'
            },
            {
                id: 2,
                name: '<PERSON>',
                company: 'Tech Solutions ME',
                role: 'Desenvolvedor',
                text: 'Como MEI, precisava de uma contabilidade que entendesse meu negócio. A Logyc superou minhas expectativas com preço justo e serviço de qualidade.',
                rating: 5,
                image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
                date: '2024-01-10'
            },
            {
                id: 3,
                name: 'Ana Costa',
                company: 'Boutique Ana LTDA',
                role: 'Comerciante',
                text: 'Migrei de outra contabilidade e foi a melhor decisão! A Logyc cuidou de toda a transferência sem complicações. Recomendo!',
                rating: 5,
                image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
                date: '2024-01-08'
            },
            {
                id: 4,
                name: 'Carlos Oliveira',
                company: 'Oliveira Consultoria',
                role: 'Consultor',
                text: 'Atendimento personalizado e equipe sempre atualizada. A calculadora do site me ajudou muito na decisão. Excelente custo-benefício!',
                rating: 5,
                image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
                date: '2024-01-05'
            },
            {
                id: 5,
                name: 'Fernanda Lima',
                company: 'Lima Arquitetura',
                role: 'Arquiteta',
                text: 'Profissionais sérios e comprometidos. Cuidam de toda parte fiscal e pessoal da minha empresa com muita competência.',
                rating: 5,
                image: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
                date: '2024-01-03'
            },
            {
                id: 6,
                name: 'Roberto Mendes',
                company: 'Mendes Transportes',
                role: 'Empresário',
                text: 'Mesmo estando em outro estado, o atendimento é impecável. A tecnologia que eles usam facilita muito nossa comunicação.',
                rating: 5,
                image: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
                date: '2024-01-01'
            }
        ];
    }

    createTestimonialsSection() {
        // Verificar se já existe
        let testimonialsSection = document.getElementById('testimonials-section');

        if (!testimonialsSection) {
            testimonialsSection = document.createElement('section');
            testimonialsSection.id = 'testimonials-section';
            testimonialsSection.className = 'testimonials-section';

            // Inserir após a seção de serviços
            const servicesSection = document.getElementById('servicos');
            if (servicesSection) {
                servicesSection.parentNode.insertBefore(testimonialsSection, servicesSection.nextSibling);
            } else {
                // Inserir antes do FAQ
                const faqSection = document.getElementById('faq-section');
                if (faqSection) {
                    faqSection.parentNode.insertBefore(testimonialsSection, faqSection);
                } else {
                    document.body.appendChild(testimonialsSection);
                }
            }
        }

        testimonialsSection.innerHTML = `
            <div class="container">
                <div class="testimonials-header">
                    <h2>O que nossos clientes dizem</h2>
                    <p>Depoimentos reais de empresários que confiam na Logyc</p>
                </div>

                <div class="testimonials-carousel">
                    <div class="testimonials-container" id="testimonialsContainer">
                        <!-- Depoimentos serão inseridos aqui -->
                    </div>

                    <div class="testimonials-navigation">
                        <button class="testimonial-nav prev" id="prevTestimonial">‹</button>
                        <div class="testimonials-dots" id="testimonialsDots">
                            <!-- Dots serão inseridos aqui -->
                        </div>
                        <button class="testimonial-nav next" id="nextTestimonial">›</button>
                    </div>
                </div>

                <div class="testimonials-stats">
                    <div class="stat-item">
                        <div class="stat-number">500+</div>
                        <div class="stat-label">Clientes Ativos</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">5 Anos</div>
                        <div class="stat-label">de Experiência</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">98%</div>
                        <div class="stat-label">Satisfação</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">24h</div>
                        <div class="stat-label">Tempo Resposta</div>
                    </div>
                </div>
            </div>
        `;

        this.setupStyles();
    }

    setupStyles() {
        const styles = `
            .testimonials-section {
                padding: 80px 0;
                background: var(--branco, #ffffff);
                overflow: hidden;
            }

            .testimonials-header {
                text-align: center;
                margin-bottom: 3rem;
            }

            .testimonials-header h2 {
                color: var(--azul-escuro, #005aec);
                font-size: 2.5rem;
                margin-bottom: 1rem;
            }

            .testimonials-header p {
                color: var(--cinza-escuro, #666);
                font-size: 1.1rem;
            }

            .testimonials-carousel {
                position: relative;
                max-width: 1000px;
                margin: 0 auto;
            }

            .testimonials-container {
                display: flex;
                transition: transform 0.5s ease;
                gap: 2rem;
            }

            .testimonial-card {
                min-width: 100%;
                background: var(--cinza-claro, #f8f9fa);
                border-radius: 15px;
                padding: 2rem;
                text-align: center;
                box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
                position: relative;
            }

            .testimonial-quote {
                font-size: 3rem;
                color: var(--azul-escuro, #005aec);
                opacity: 0.3;
                position: absolute;
                top: 1rem;
                left: 2rem;
            }

            .testimonial-text {
                font-size: 1.1rem;
                line-height: 1.6;
                color: var(--cinza-escuro, #333);
                margin: 2rem 0;
                font-style: italic;
            }

            .testimonial-author {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 1rem;
                margin-top: 2rem;
            }

            .author-image {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                object-fit: cover;
                border: 3px solid var(--azul-escuro, #005aec);
            }

            .author-info h4 {
                color: var(--azul-escuro, #005aec);
                margin-bottom: 0.25rem;
                font-size: 1.1rem;
            }

            .author-company {
                color: var(--cinza-escuro, #666);
                font-size: 0.9rem;
                margin-bottom: 0.25rem;
            }

            .author-role {
                color: var(--cinza-escuro, #999);
                font-size: 0.8rem;
            }

            .testimonial-rating {
                display: flex;
                justify-content: center;
                gap: 0.25rem;
                margin: 1rem 0;
            }

            .star {
                color: var(--amarelo, #ffe206);
                font-size: 1.2rem;
            }

            .testimonials-navigation {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 2rem;
                margin-top: 2rem;
            }

            .testimonial-nav {
                background: var(--azul-escuro, #005aec);
                color: white;
                border: none;
                border-radius: 50%;
                width: 50px;
                height: 50px;
                font-size: 1.5rem;
                cursor: pointer;
                transition: all 0.3s;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .testimonial-nav:hover {
                background: var(--azul-claro, #0593ff);
                transform: scale(1.1);
            }

            .testimonial-nav:disabled {
                background: #ccc;
                cursor: not-allowed;
                transform: none;
            }

            .testimonials-dots {
                display: flex;
                gap: 0.5rem;
            }

            .dot {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: #ddd;
                cursor: pointer;
                transition: all 0.3s;
            }

            .dot.active {
                background: var(--azul-escuro, #005aec);
                transform: scale(1.2);
            }

            .testimonials-stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 2rem;
                margin-top: 4rem;
                padding: 2rem 0;
                border-top: 1px solid #eee;
            }

            .stat-item {
                text-align: center;
            }

            .stat-number {
                font-size: 2.5rem;
                font-weight: bold;
                color: var(--azul-escuro, #005aec);
                margin-bottom: 0.5rem;
            }

            .stat-label {
                color: var(--cinza-escuro, #666);
                font-size: 1rem;
            }

            @media (max-width: 768px) {
                .testimonials-section {
                    padding: 60px 0;
                }

                .testimonials-header h2 {
                    font-size: 2rem;
                }

                .testimonial-card {
                    padding: 1.5rem;
                }

                .testimonial-text {
                    font-size: 1rem;
                }

                .testimonial-author {
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .author-image {
                    width: 50px;
                    height: 50px;
                }

                .testimonials-stats {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 1rem;
                }

                .stat-number {
                    font-size: 2rem;
                }
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    renderTestimonials() {
        const container = document.getElementById('testimonialsContainer');
        const dotsContainer = document.getElementById('testimonialsDots');

        // Renderizar cards
        container.innerHTML = this.testimonials.map(testimonial => `
            <div class="testimonial-card">
                <div class="testimonial-quote">"</div>
                <div class="testimonial-rating">
                    ${this.renderStars(testimonial.rating)}
                </div>
                <div class="testimonial-text">${testimonial.text}</div>
                <div class="testimonial-author">
                    <img src="${testimonial.image}" alt="${testimonial.name}" class="author-image" loading="lazy">
                    <div class="author-info">
                        <h4>${testimonial.name}</h4>
                        <div class="author-company">${testimonial.company}</div>
                        <div class="author-role">${testimonial.role}</div>
                    </div>
                </div>
            </div>
        `).join('');

        // Renderizar dots
        dotsContainer.innerHTML = this.testimonials.map((_, index) => `
            <div class="dot ${index === this.currentIndex ? 'active' : ''}" data-index="${index}"></div>
        `).join('');

        this.updateCarousel();
    }

    renderStars(rating) {
        return Array.from({ length: 5 }, (_, i) =>
            `<span class="star">${i < rating ? '★' : '☆'}</span>`
        ).join('');
    }

    setupEventListeners() {
        const prevBtn = document.getElementById('prevTestimonial');
        const nextBtn = document.getElementById('nextTestimonial');
        const dotsContainer = document.getElementById('testimonialsDots');

        prevBtn.addEventListener('click', () => this.prevTestimonial());
        nextBtn.addEventListener('click', () => this.nextTestimonial());

        dotsContainer.addEventListener('click', (e) => {
            if (e.target.classList.contains('dot')) {
                this.currentIndex = parseInt(e.target.dataset.index);
                this.updateCarousel();
                this.resetAutoPlay();
            }
        });

        // Pausar autoplay ao hover
        const carousel = document.querySelector('.testimonials-carousel');
        carousel.addEventListener('mouseenter', () => this.stopAutoPlay());
        carousel.addEventListener('mouseleave', () => this.startAutoPlay());
    }

    updateCarousel() {
        const container = document.getElementById('testimonialsContainer');
        const dots = document.querySelectorAll('.dot');

        container.style.transform = `translateX(-${this.currentIndex * 100}%)`;

        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === this.currentIndex);
        });

        // Rastrear visualização
        if (window.trackEvent) {
            window.trackEvent('testimonial_view', {
                testimonial_id: this.testimonials[this.currentIndex].id,
                testimonial_author: this.testimonials[this.currentIndex].name
            });
        }
    }

    nextTestimonial() {
        this.currentIndex = (this.currentIndex + 1) % this.testimonials.length;
        this.updateCarousel();
        this.resetAutoPlay();
    }

    prevTestimonial() {
        this.currentIndex = this.currentIndex === 0 ? this.testimonials.length - 1 : this.currentIndex - 1;
        this.updateCarousel();
        this.resetAutoPlay();
    }

    startAutoPlay() {
        this.autoPlayInterval = setInterval(() => {
            this.nextTestimonial();
        }, 5000);
    }

    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }

    resetAutoPlay() {
        this.stopAutoPlay();
        this.startAutoPlay();
    }

    // Método para adicionar novo depoimento
    addTestimonial(testimonial) {
        this.testimonials.push({
            id: Date.now(),
            ...testimonial
        });
        this.renderTestimonials();
    }
}

// Inicializar apenas na página principal
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname === '/' || window.location.pathname === '/index.html') {
        setTimeout(() => {
            window.testimonialsSystem = new TestimonialsSystem();
        }, 1500);
    }
});

// Exportar para uso global
window.TestimonialsSystem = TestimonialsSystem;
