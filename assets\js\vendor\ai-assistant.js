// Sistema de IA Assistant - Logyc Contabilidade
class AIAssistant {
    constructor() {
        this.knowledgeBase = this.buildKnowledgeBase();
        this.conversationHistory = [];
        this.userProfile = this.initUserProfile();
        this.contextAnalyzer = new ContextAnalyzer();
        this.responseGenerator = new ResponseGenerator();
        this.learningSystem = new LearningSystem();
        this.init();
    }
    
    init() {
        this.setupNLPProcessor();
        this.setupIntentRecognition();
        this.setupEntityExtraction();
        this.setupResponseOptimization();
        this.setupLearningLoop();
    }
    
    buildKnowledgeBase() {
        return {
            services: {
                mei: {
                    price: 75,
                    description: 'Microempreendedor Individual',
                    benefits: ['Baixo custo', 'Simplicidade', 'Regime SIMEI'],
                    requirements: ['Faturamento até R$ 81.000/ano', 'Máximo 1 funcionário'],
                    processes: ['DAS mensal', 'DASN anual', 'Relatórios mensais']
                },
                services: {
                    price_range: [285, 370],
                    description: 'Prestação de Serviços',
                    benefits: ['Simples Nacional', 'Até 3 funcionários inclusos', 'Gestão completa'],
                    requirements: ['Atividade de serviços', 'Faturamento compatível'],
                    processes: ['Apuração mensal', 'Obrigações acessórias', 'DP completo']
                },
                commerce: {
                    price_range: [245, 390],
                    description: 'Comércio',
                    benefits: ['Regime otimizado', 'Gestão de estoque', 'NFe automática'],
                    requirements: ['Atividade comercial', 'Controle de estoque'],
                    processes: ['ICMS', 'Inventário', 'Conciliação fiscal']
                },
                industry: {
                    price_range: [300, 545],
                    description: 'Indústria',
                    benefits: ['IPI otimizado', 'Créditos fiscais', 'Gestão complexa'],
                    requirements: ['Atividade industrial', 'Processo produtivo'],
                    processes: ['Apuração IPI', 'Créditos ICMS', 'Custos industriais']
                }
            },
            
            processes: {
                opening: {
                    steps: ['Consulta viabilidade', 'Contrato social', 'CNPJ', 'Inscrições', 'Alvarás'],
                    timeline: '15-30 dias',
                    documents: ['RG/CPF sócios', 'Comprovante endereço', 'Contrato locação'],
                    cost: 'Consulte'
                },
                switching: {
                    steps: ['Análise situação', 'Solicitação arquivos', 'Transferência', 'Regularização'],
                    timeline: '5-10 dias',
                    documents: ['Procuração', 'Últimas declarações', 'Balancetes'],
                    benefits: ['Sem interrupção', 'Regularização incluída', 'Suporte total']
                }
            },
            
            compliance: {
                monthly: ['DAS/GPS', 'DCTF', 'Relatórios gerenciais', 'Conciliações'],
                annual: ['DASN', 'RAIS', 'DIRF', 'Balanço'],
                occasional: ['Parcelamentos', 'Restituições', 'Consultorias']
            },
            
            faqs: {
                'prazo abertura': 'O prazo para abertura varia de 15 a 30 dias úteis, dependendo da atividade e órgãos envolvidos.',
                'custo mei': 'Para MEI, nossa mensalidade é R$ 75,00 + R$ 40,00 por funcionário.',
                'documentos necessários': 'RG, CPF, comprovante de residência dos sócios e contrato de locação do imóvel.',
                'troca contabilidade': 'Cuidamos de todo processo de transferência sem interrupção dos serviços.',
                'simples nacional': 'Regime tributário simplificado com alíquotas reduzidas e menos obrigações.'
            }
        };
    }
    
    initUserProfile() {
        const stored = localStorage.getItem('logyc_user_profile');
        if (stored) {
            return JSON.parse(stored);
        }
        
        return {
            id: this.generateUserId(),
            interests: [],
            company_type: null,
            revenue_range: null,
            employees: null,
            current_accountant: null,
            pain_points: [],
            interaction_history: [],
            preferences: {
                communication_style: 'professional',
                detail_level: 'medium',
                response_speed: 'normal'
            },
            created_at: new Date().toISOString()
        };
    }
    
    setupNLPProcessor() {
        this.nlp = {
            tokenize: (text) => {
                return text.toLowerCase()
                    .replace(/[^\w\s]/g, ' ')
                    .split(/\s+/)
                    .filter(token => token.length > 2);
            },
            
            extractKeywords: (text) => {
                const tokens = this.nlp.tokenize(text);
                const keywords = [];
                
                // Business keywords
                const businessTerms = ['empresa', 'negócio', 'cnpj', 'mei', 'ltda', 'sociedade'];
                const serviceTerms = ['contabilidade', 'contador', 'fiscal', 'tributário', 'imposto'];
                const actionTerms = ['abrir', 'trocar', 'mudar', 'contratar', 'solicitar'];
                
                tokens.forEach(token => {
                    if (businessTerms.includes(token)) keywords.push({type: 'business', value: token});
                    if (serviceTerms.includes(token)) keywords.push({type: 'service', value: token});
                    if (actionTerms.includes(token)) keywords.push({type: 'action', value: token});
                });
                
                return keywords;
            },
            
            detectIntent: (text) => {
                const intents = {
                    'pricing': ['preço', 'valor', 'custo', 'quanto', 'mensalidade'],
                    'opening': ['abrir', 'abertura', 'criar', 'constituir', 'nova empresa'],
                    'switching': ['trocar', 'mudar', 'transferir', 'migrar', 'sair'],
                    'services': ['serviços', 'oferece', 'faz', 'atende', 'trabalha'],
                    'mei': ['mei', 'microempreendedor', 'individual'],
                    'documents': ['documentos', 'papéis', 'precisa', 'necessário'],
                    'timeline': ['prazo', 'tempo', 'demora', 'quando', 'rapidez'],
                    'contact': ['contato', 'falar', 'conversar', 'atendimento']
                };
                
                const tokens = this.nlp.tokenize(text);
                const scores = {};
                
                Object.keys(intents).forEach(intent => {
                    scores[intent] = 0;
                    intents[intent].forEach(keyword => {
                        if (tokens.includes(keyword)) {
                            scores[intent] += 1;
                        }
                    });
                });
                
                const maxScore = Math.max(...Object.values(scores));
                const detectedIntent = Object.keys(scores).find(intent => scores[intent] === maxScore);
                
                return {
                    intent: maxScore > 0 ? detectedIntent : 'general',
                    confidence: maxScore / tokens.length,
                    scores: scores
                };
            },
            
            extractEntities: (text) => {
                const entities = {};
                const tokens = this.nlp.tokenize(text);
                
                // Extract numbers (revenue, employees)
                const numbers = text.match(/\d+/g);
                if (numbers) {
                    entities.numbers = numbers.map(n => parseInt(n));
                }
                
                // Extract company types
                const companyTypes = ['mei', 'ltda', 'sa', 'eireli', 'sociedade'];
                entities.company_types = tokens.filter(token => companyTypes.includes(token));
                
                // Extract locations
                const locations = ['curitiba', 'paraná', 'brasil', 'pr'];
                entities.locations = tokens.filter(token => locations.includes(token));
                
                return entities;
            }
        };
    }
    
    setupIntentRecognition() {
        this.intentClassifier = {
            classify: (message) => {
                const analysis = this.nlp.detectIntent(message);
                const entities = this.nlp.extractEntities(message);
                const keywords = this.nlp.extractKeywords(message);
                
                return {
                    primary_intent: analysis.intent,
                    confidence: analysis.confidence,
                    entities: entities,
                    keywords: keywords,
                    context: this.contextAnalyzer.analyze(message, this.conversationHistory)
                };
            }
        };
    }
    
    setupEntityExtraction() {
        this.entityExtractor = {
            extract: (message, intent) => {
                const entities = this.nlp.extractEntities(message);
                
                // Context-aware extraction based on intent
                switch (intent) {
                    case 'pricing':
                        return this.extractPricingEntities(message, entities);
                    case 'opening':
                        return this.extractOpeningEntities(message, entities);
                    case 'switching':
                        return this.extractSwitchingEntities(message, entities);
                    default:
                        return entities;
                }
            },
            
            extractPricingEntities: (message, entities) => {
                // Extract revenue range
                if (entities.numbers) {
                    entities.revenue = entities.numbers.find(n => n > 1000);
                }
                
                // Extract employee count
                const employeeKeywords = ['funcionário', 'empregado', 'colaborador'];
                if (employeeKeywords.some(keyword => message.includes(keyword))) {
                    entities.employees = entities.numbers?.find(n => n < 100) || 0;
                }
                
                return entities;
            }
        };
    }
    
    setupResponseOptimization() {
        this.responseOptimizer = {
            optimize: (response, userProfile, context) => {
                // Personalize based on user profile
                if (userProfile.preferences.communication_style === 'casual') {
                    response = this.makeCasual(response);
                } else if (userProfile.preferences.communication_style === 'technical') {
                    response = this.makeTechnical(response);
                }
                
                // Adjust detail level
                if (userProfile.preferences.detail_level === 'high') {
                    response = this.addDetails(response, context);
                } else if (userProfile.preferences.detail_level === 'low') {
                    response = this.simplify(response);
                }
                
                return response;
            },
            
            makeCasual: (response) => {
                return response
                    .replace('Prezado cliente', 'Oi')
                    .replace('Atenciosamente', 'Abraços')
                    .replace('Informamos que', 'Olha só');
            },
            
            makeTechnical: (response) => {
                // Add technical details and references
                return response + '\n\nPara mais detalhes técnicos, consulte nossa documentação ou fale com nossos especialistas.';
            }
        };
    }
    
    setupLearningLoop() {
        this.learningSystem = {
            learn: (interaction) => {
                // Store successful interactions
                const learningData = {
                    input: interaction.message,
                    intent: interaction.intent,
                    response: interaction.response,
                    user_feedback: interaction.feedback,
                    context: interaction.context,
                    timestamp: new Date().toISOString()
                };
                
                this.storeLearningData(learningData);
                this.updateUserProfile(interaction);
                this.optimizeResponses(learningData);
            },
            
            storeLearningData: (data) => {
                const stored = JSON.parse(localStorage.getItem('logyc_ai_learning') || '[]');
                stored.push(data);
                
                // Keep only last 100 interactions
                if (stored.length > 100) {
                    stored.splice(0, stored.length - 100);
                }
                
                localStorage.setItem('logyc_ai_learning', JSON.stringify(stored));
            }
        };
    }
    
    async processMessage(message, context = {}) {
        try {
            // Analyze message
            const analysis = this.intentClassifier.classify(message);
            
            // Extract entities
            const entities = this.entityExtractor.extract(message, analysis.primary_intent);
            
            // Generate response
            const response = await this.generateResponse(analysis, entities, context);
            
            // Optimize response
            const optimizedResponse = this.responseOptimizer.optimize(response, this.userProfile, context);
            
            // Store interaction
            const interaction = {
                message: message,
                intent: analysis.primary_intent,
                entities: entities,
                response: optimizedResponse,
                context: context,
                timestamp: new Date().toISOString()
            };
            
            this.conversationHistory.push(interaction);
            this.learningSystem.learn(interaction);
            
            return {
                response: optimizedResponse,
                intent: analysis.primary_intent,
                confidence: analysis.confidence,
                suggestions: this.generateSuggestions(analysis, entities),
                actions: this.generateActions(analysis, entities)
            };
            
        } catch (error) {
            console.error('AI Assistant Error:', error);
            return {
                response: 'Desculpe, tive um problema para processar sua mensagem. Pode reformular?',
                intent: 'error',
                confidence: 0,
                suggestions: ['Falar com atendente humano'],
                actions: [{ type: 'whatsapp', text: 'Falar no WhatsApp' }]
            };
        }
    }
    
    async generateResponse(analysis, entities, context) {
        const intent = analysis.primary_intent;
        const knowledgeBase = this.knowledgeBase;
        
        switch (intent) {
            case 'pricing':
                return this.generatePricingResponse(entities);
            
            case 'opening':
                return this.generateOpeningResponse(entities);
            
            case 'switching':
                return this.generateSwitchingResponse(entities);
            
            case 'services':
                return this.generateServicesResponse();
            
            case 'mei':
                return this.generateMEIResponse();
            
            case 'documents':
                return this.generateDocumentsResponse(entities);
            
            case 'timeline':
                return this.generateTimelineResponse(entities);
            
            case 'contact':
                return this.generateContactResponse();
            
            default:
                return this.generateGeneralResponse(analysis, entities);
        }
    }
    
    generatePricingResponse(entities) {
        let response = "Nossos preços variam conforme o tipo de empresa:\n\n";
        
        if (entities.company_types?.includes('mei')) {
            response += "🔹 MEI: R$ 75/mês + R$ 40 por funcionário\n";
        } else {
            response += "🔹 MEI: R$ 75/mês + R$ 40 por funcionário\n";
            response += "🔹 Serviços: R$ 285 a R$ 370/mês\n";
            response += "🔹 Comércio: R$ 245 a R$ 390/mês\n";
            response += "🔹 Indústria: R$ 300 a R$ 545/mês\n\n";
        }
        
        if (entities.revenue || entities.employees) {
            response += "Com base nas informações que você mencionou, posso fazer um cálculo mais preciso. ";
        }
        
        response += "Quer usar nossa calculadora para ter um valor exato?";
        
        return response;
    }
    
    generateOpeningResponse(entities) {
        let response = "Para abertura de empresa, cuidamos de todo o processo:\n\n";
        response += "📋 Etapas:\n";
        response += "• Consulta de viabilidade\n";
        response += "• Elaboração do contrato social\n";
        response += "• Registro na Junta Comercial\n";
        response += "• Obtenção do CNPJ\n";
        response += "• Inscrições municipais e estaduais\n";
        response += "• Alvarás e licenças\n\n";
        response += "⏱️ Prazo: 15 a 30 dias úteis\n\n";
        response += "Precisa de alguma informação específica sobre abertura?";
        
        return response;
    }
    
    generateSuggestions(analysis, entities) {
        const suggestions = [];
        
        switch (analysis.primary_intent) {
            case 'pricing':
                suggestions.push('Usar calculadora', 'Ver serviços inclusos', 'Comparar planos');
                break;
            case 'opening':
                suggestions.push('Documentos necessários', 'Prazo de abertura', 'Custo do processo');
                break;
            case 'switching':
                suggestions.push('Como funciona a troca', 'Documentos para transferência', 'Tempo de migração');
                break;
            default:
                suggestions.push('Ver preços', 'Falar com especialista', 'Usar calculadora');
        }
        
        return suggestions;
    }
    
    generateActions(analysis, entities) {
        const actions = [];
        
        switch (analysis.primary_intent) {
            case 'pricing':
                actions.push(
                    { type: 'calculator', text: 'Calcular Preço', url: '/calculadora.html' },
                    { type: 'whatsapp', text: 'Falar com Especialista' }
                );
                break;
            case 'opening':
                actions.push(
                    { type: 'form', text: 'Solicitar Abertura', url: '/como-podemos-ajudar.html' },
                    { type: 'whatsapp', text: 'Tirar Dúvidas' }
                );
                break;
            default:
                actions.push(
                    { type: 'whatsapp', text: 'Falar no WhatsApp' }
                );
        }
        
        return actions;
    }
    
    updateUserProfile(interaction) {
        // Update interests based on intents
        if (!this.userProfile.interests.includes(interaction.intent)) {
            this.userProfile.interests.push(interaction.intent);
        }
        
        // Extract company info from entities
        if (interaction.entities.company_types?.length > 0) {
            this.userProfile.company_type = interaction.entities.company_types[0];
        }
        
        if (interaction.entities.revenue) {
            this.userProfile.revenue_range = interaction.entities.revenue;
        }
        
        if (interaction.entities.employees !== undefined) {
            this.userProfile.employees = interaction.entities.employees;
        }
        
        // Update interaction history
        this.userProfile.interaction_history.push({
            intent: interaction.intent,
            timestamp: interaction.timestamp
        });
        
        // Save profile
        localStorage.setItem('logyc_user_profile', JSON.stringify(this.userProfile));
    }
    
    generateUserId() {
        return 'ai_user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    // Public methods
    getConversationHistory() {
        return this.conversationHistory;
    }
    
    getUserProfile() {
        return this.userProfile;
    }
    
    resetConversation() {
        this.conversationHistory = [];
        localStorage.removeItem('logyc_ai_learning');
    }
    
    exportLearningData() {
        return {
            conversations: this.conversationHistory,
            user_profile: this.userProfile,
            learning_data: JSON.parse(localStorage.getItem('logyc_ai_learning') || '[]')
        };
    }
}

// Context Analyzer Class
class ContextAnalyzer {
    analyze(message, history) {
        return {
            previous_intent: history.length > 0 ? history[history.length - 1].intent : null,
            conversation_length: history.length,
            topics_discussed: this.extractTopics(history),
            user_sentiment: this.analyzeSentiment(message),
            urgency_level: this.detectUrgency(message)
        };
    }
    
    extractTopics(history) {
        const topics = new Set();
        history.forEach(interaction => {
            topics.add(interaction.intent);
        });
        return Array.from(topics);
    }
    
    analyzeSentiment(message) {
        const positiveWords = ['bom', 'ótimo', 'excelente', 'gostei', 'perfeito'];
        const negativeWords = ['ruim', 'péssimo', 'problema', 'difícil', 'complicado'];
        
        const words = message.toLowerCase().split(' ');
        let score = 0;
        
        words.forEach(word => {
            if (positiveWords.includes(word)) score += 1;
            if (negativeWords.includes(word)) score -= 1;
        });
        
        if (score > 0) return 'positive';
        if (score < 0) return 'negative';
        return 'neutral';
    }
    
    detectUrgency(message) {
        const urgentWords = ['urgente', 'rápido', 'hoje', 'agora', 'preciso'];
        const words = message.toLowerCase().split(' ');
        
        return urgentWords.some(word => words.includes(word)) ? 'high' : 'normal';
    }
}

// Response Generator Class
class ResponseGenerator {
    constructor() {
        this.templates = this.loadTemplates();
    }
    
    loadTemplates() {
        return {
            greeting: [
                "Olá! Sou o assistente virtual da Logyc. Como posso ajudar?",
                "Oi! Em que posso te ajudar hoje?",
                "Bem-vindo à Logyc! Como posso te auxiliar?"
            ],
            pricing: [
                "Nossos preços são transparentes e competitivos...",
                "Temos planos para todos os tipos de empresa..."
            ],
            closing: [
                "Espero ter ajudado! Precisa de mais alguma coisa?",
                "Foi um prazer te ajudar! Tem mais alguma dúvida?"
            ]
        };
    }
}

// Learning System Class
class LearningSystem {
    constructor() {
        this.patterns = new Map();
        this.successfulResponses = new Map();
    }
    
    learn(interaction) {
        // Learn from successful interactions
        if (interaction.feedback === 'positive') {
            const key = `${interaction.intent}_${interaction.entities}`;
            this.successfulResponses.set(key, interaction.response);
        }
        
        // Identify patterns
        this.identifyPatterns(interaction);
    }
    
    identifyPatterns(interaction) {
        // Pattern recognition logic
        const pattern = {
            intent: interaction.intent,
            entities: interaction.entities,
            context: interaction.context,
            response_type: this.classifyResponse(interaction.response)
        };
        
        const patternKey = JSON.stringify(pattern);
        const count = this.patterns.get(patternKey) || 0;
        this.patterns.set(patternKey, count + 1);
    }
    
    classifyResponse(response) {
        if (response.includes('R$')) return 'pricing';
        if (response.includes('prazo')) return 'timeline';
        if (response.includes('documento')) return 'documentation';
        return 'general';
    }
}

// Initialize AI Assistant
document.addEventListener('DOMContentLoaded', function() {
    window.aiAssistant = new AIAssistant();
    
    // Integrate with existing chat system
    if (window.chatSystem) {
        // Override chat system's response generation
        const originalGenerateResponse = window.chatSystem.generateResponse;
        window.chatSystem.generateResponse = async function(message) {
            try {
                const aiResponse = await window.aiAssistant.processMessage(message);
                return {
                    text: aiResponse.response,
                    actions: aiResponse.actions?.map(action => action.text) || []
                };
            } catch (error) {
                // Fallback to original system
                return originalGenerateResponse.call(this, message);
            }
        };
    }
});

// Export for global use
window.AIAssistant = AIAssistant;
