// Sistema de Validação Avançada para Logyc Contabilidade
class FormValidator {
    constructor(formId) {
        this.form = document.getElementById(formId);
        this.errors = new Map();
        this.rules = new Map();
        this.init();
    }
    
    init() {
        if (!this.form) return;
        
        // Configurar validação em tempo real
        this.setupRealTimeValidation();
        
        // Configurar validação no submit
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            if (this.validateForm()) {
                this.handleSubmit();
            }
        });
    }
    
    // Adicionar regra de validação personalizada
    addRule(fieldName, validator, errorMessage) {
        if (!this.rules.has(fieldName)) {
            this.rules.set(fieldName, []);
        }
        this.rules.get(fieldName).push({ validator, errorMessage });
    }
    
    // Configurar validação em tempo real
    setupRealTimeValidation() {
        const inputs = this.form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            // Validação durante digitação (debounced)
            let timeout;
            input.addEventListener('input', () => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    this.validateField(input);
                }, 300);
            });
            
            // Validação ao sair do campo
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
            
            // Limpar erro ao focar
            input.addEventListener('focus', () => {
                this.clearFieldError(input);
            });
        });
    }
    
    // Validar campo individual
    validateField(field) {
        const fieldName = field.name || field.id;
        const value = field.value.trim();
        
        // Limpar erros anteriores
        this.errors.delete(fieldName);
        
        // Validação de campo obrigatório
        if (field.hasAttribute('required') && !value) {
            this.addError(fieldName, 'Este campo é obrigatório');
            this.showFieldError(field, 'Este campo é obrigatório');
            return false;
        }
        
        // Validações específicas por tipo
        if (value) {
            switch (field.type) {
                case 'email':
                    if (!this.isValidEmail(value)) {
                        this.addError(fieldName, 'Email inválido');
                        this.showFieldError(field, 'Email inválido');
                        return false;
                    }
                    break;
                    
                case 'tel':
                    if (!this.isValidPhone(value)) {
                        this.addError(fieldName, 'Telefone inválido');
                        this.showFieldError(field, 'Telefone inválido (mínimo 10 dígitos)');
                        return false;
                    }
                    break;
                    
                case 'number':
                    if (isNaN(value) || parseFloat(value) < 0) {
                        this.addError(fieldName, 'Número inválido');
                        this.showFieldError(field, 'Digite um número válido');
                        return false;
                    }
                    break;
            }
            
            // Validações específicas por nome do campo
            if (fieldName === 'cnpj' && !this.isValidCNPJ(value.replace(/\D/g, ''))) {
                this.addError(fieldName, 'CNPJ inválido');
                this.showFieldError(field, 'CNPJ inválido');
                return false;
            }
            
            if (fieldName === 'nome' && value.length < 2) {
                this.addError(fieldName, 'Nome muito curto');
                this.showFieldError(field, 'Nome deve ter pelo menos 2 caracteres');
                return false;
            }
            
            if (fieldName === 'cidade' && value.length < 2) {
                this.addError(fieldName, 'Cidade inválida');
                this.showFieldError(field, 'Digite uma cidade válida');
                return false;
            }
        }
        
        // Validações customizadas
        if (this.rules.has(fieldName)) {
            const rules = this.rules.get(fieldName);
            for (const rule of rules) {
                if (!rule.validator(value, field)) {
                    this.addError(fieldName, rule.errorMessage);
                    this.showFieldError(field, rule.errorMessage);
                    return false;
                }
            }
        }
        
        // Se chegou até aqui, campo é válido
        this.clearFieldError(field);
        return true;
    }
    
    // Validar formulário completo
    validateForm() {
        const inputs = this.form.querySelectorAll('input, textarea, select');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });
        
        // Validações específicas do formulário
        if (this.form.id === 'helpForm' || this.form.id === 'contactForm') {
            isValid = this.validateHelpForm() && isValid;
        } else if (this.form.id === 'switchForm') {
            isValid = this.validateSwitchForm() && isValid;
        }
        
        return isValid;
    }
    
    // Validações específicas do formulário de ajuda
    validateHelpForm() {
        const opcao = this.form.querySelector('input[name="opcao"]:checked');
        const mensagem = this.form.querySelector('#mensagem');
        
        if (!opcao) {
            this.showFormError('Selecione uma opção de serviço');
            return false;
        }
        
        if (opcao.value === 'outros' && (!mensagem.value || mensagem.value.trim().length < 10)) {
            this.showFieldError(mensagem, 'Descreva detalhadamente como podemos ajudar (mínimo 10 caracteres)');
            return false;
        }
        
        return true;
    }
    
    // Validações específicas do formulário de troca
    validateSwitchForm() {
        const possuiCNPJ = this.form.querySelector('input[name="possui_cnpj"]:checked');
        const cnpjField = this.form.querySelector('#cnpj');
        const possuiFuncionarios = this.form.querySelector('input[name="possui_funcionarios"]:checked');
        const funcionariosField = this.form.querySelector('#qtd_funcionarios');
        
        if (!possuiCNPJ) {
            this.showFormError('Informe se possui CNPJ');
            return false;
        }
        
        if (possuiCNPJ.value === 'sim' && (!cnpjField.value || !this.isValidCNPJ(cnpjField.value.replace(/\D/g, '')))) {
            this.showFieldError(cnpjField, 'CNPJ obrigatório e deve ser válido');
            return false;
        }
        
        if (!possuiFuncionarios) {
            this.showFormError('Informe se possui funcionários');
            return false;
        }
        
        if (possuiFuncionarios.value === 'sim' && (!funcionariosField.value || parseInt(funcionariosField.value) < 1)) {
            this.showFieldError(funcionariosField, 'Informe a quantidade de funcionários (mínimo 1)');
            return false;
        }
        
        return true;
    }
    
    // Adicionar erro
    addError(fieldName, message) {
        this.errors.set(fieldName, message);
    }
    
    // Mostrar erro no campo
    showFieldError(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('error');
        field.setAttribute('aria-invalid', 'true');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        errorDiv.setAttribute('role', 'alert');
        
        field.parentNode.appendChild(errorDiv);
        
        // Anunciar erro para leitores de tela
        if (window.announceToScreenReader) {
            window.announceToScreenReader(`Erro: ${message}`);
        }
    }
    
    // Limpar erro do campo
    clearFieldError(field) {
        field.classList.remove('error');
        field.removeAttribute('aria-invalid');
        
        const errorDiv = field.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }
    
    // Mostrar erro geral do formulário
    showFormError(message) {
        const existingError = this.form.querySelector('.form-error');
        if (existingError) {
            existingError.remove();
        }
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'form-error';
        errorDiv.textContent = message;
        errorDiv.setAttribute('role', 'alert');
        
        this.form.insertBefore(errorDiv, this.form.firstChild);
        
        // Scroll para o erro
        errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Anunciar erro
        if (window.announceToScreenReader) {
            window.announceToScreenReader(`Erro no formulário: ${message}`);
        }
    }
    
    // Validações auxiliares
    isValidEmail(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    }
    
    isValidPhone(phone) {
        const cleanPhone = phone.replace(/\D/g, '');
        return cleanPhone.length >= 10 && cleanPhone.length <= 15;
    }
    
    isValidCNPJ(cnpj) {
        if (cnpj.length !== 14) return false;
        if (/^(\d)\1+$/.test(cnpj)) return false;
        
        // Validação dos dígitos verificadores
        let tamanho = cnpj.length - 2;
        let numeros = cnpj.substring(0, tamanho);
        let digitos = cnpj.substring(tamanho);
        let soma = 0;
        let pos = tamanho - 7;
        
        for (let i = tamanho; i >= 1; i--) {
            soma += numeros.charAt(tamanho - i) * pos--;
            if (pos < 2) pos = 9;
        }
        
        let resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
        if (resultado != digitos.charAt(0)) return false;
        
        tamanho = tamanho + 1;
        numeros = cnpj.substring(0, tamanho);
        soma = 0;
        pos = tamanho - 7;
        
        for (let i = tamanho; i >= 1; i--) {
            soma += numeros.charAt(tamanho - i) * pos--;
            if (pos < 2) pos = 9;
        }
        
        resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
        return resultado == digitos.charAt(1);
    }
    
    // Manipular envio do formulário
    handleSubmit() {
        // Implementar lógica específica de cada formulário
        if (this.form.id === 'helpForm') {
            this.handleHelpFormSubmit();
        } else if (this.form.id === 'switchForm') {
            this.handleSwitchFormSubmit();
        } else if (this.form.id === 'contactForm') {
            this.handleContactFormSubmit();
        }
    }
    
    handleHelpFormSubmit() {
        if (typeof handleHelpFormSubmission === 'function') {
            handleHelpFormSubmission();
        }
    }
    
    handleSwitchFormSubmit() {
        if (typeof handleSwitchFormSubmission === 'function') {
            handleSwitchFormSubmission();
        }
    }
    
    handleContactFormSubmit() {
        if (typeof handleFormSubmission === 'function') {
            handleFormSubmission();
        }
    }
}

// CSS para validação
const validationCSS = `
    .field-error {
        color: #fd0e35;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }
    
    .form-error {
        background: #fd0e35;
        color: white;
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
        text-align: center;
        font-weight: bold;
    }
    
    .error {
        border-color: #fd0e35 !important;
        box-shadow: 0 0 5px rgba(253, 14, 53, 0.3) !important;
    }
    
    .valid {
        border-color: #01d800 !important;
        box-shadow: 0 0 5px rgba(1, 216, 0, 0.3) !important;
    }
    
    .loading {
        opacity: 0.7;
        pointer-events: none;
    }
    
    .success-message {
        background: #01d800;
        color: white;
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
        text-align: center;
        font-weight: bold;
    }
`;

// Adicionar CSS
const validationStyle = document.createElement('style');
validationStyle.textContent = validationCSS;
document.head.appendChild(validationStyle);

// Inicializar validadores quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar validadores para cada formulário
    if (document.getElementById('helpForm')) {
        window.helpFormValidator = new FormValidator('helpForm');
    }
    
    if (document.getElementById('switchForm')) {
        window.switchFormValidator = new FormValidator('switchForm');
    }
    
    if (document.getElementById('contactForm')) {
        window.contactFormValidator = new FormValidator('contactForm');
    }
});

// Exportar classe para uso global
window.FormValidator = FormValidator;
