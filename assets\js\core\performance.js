// Otimizações de Performance para Logyc Contabilidade

// Lazy Loading para imagens
function initLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    } else {
        // Fallback para navegadores sem suporte
        document.querySelectorAll('img[data-src]').forEach(img => {
            img.src = img.dataset.src;
        });
    }
}

// Preload de recursos críticos
function preloadCriticalResources() {
    const criticalResources = [
        { href: '/styles.css', as: 'style' },
        { href: '/forms.css', as: 'style' },
        { href: '/logo.png', as: 'image' }
    ];

    criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource.href;
        link.as = resource.as;
        document.head.appendChild(link);
    });
}

// Debounce para eventos de scroll e resize
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// Otimização de scroll
const optimizedScroll = debounce(() => {
    // Lógica de scroll otimizada
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // Parallax removido para evitar conflitos - agora gerenciado pelo modern-ui.js

    // Mostrar/esconder header baseado no scroll
    const header = document.querySelector('.header');
    if (header) {
        if (scrollTop > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    }
}, 16); // ~60fps

// Otimização de resize
const optimizedResize = debounce(() => {
    // Recalcular layouts se necessário
    const calculatorWrapper = document.querySelector('.calculator-wrapper');
    if (calculatorWrapper && window.innerWidth < 768) {
        calculatorWrapper.classList.add('mobile-layout');
    } else if (calculatorWrapper) {
        calculatorWrapper.classList.remove('mobile-layout');
    }
}, 250);

// Minificação de CSS crítico inline
function inlineCriticalCSS() {
    const criticalCSS = `
        .header{position:fixed;top:0;width:100%;z-index:1000;background:#fff;box-shadow:0 2px 10px rgba(0,0,0,0.1)}
        .hero{background:linear-gradient(135deg,#005aec,#0593ff);color:#fff;padding:120px 0 80px;text-align:center}
        .container{max-width:1200px;margin:0 auto;padding:0 20px}
    `;

    const style = document.createElement('style');
    style.textContent = criticalCSS;
    document.head.insertBefore(style, document.head.firstChild);
}

// Carregamento assíncrono de recursos não críticos
function loadNonCriticalResources() {
    // Carregar fontes de forma otimizada
    const fontLink = document.createElement('link');
    fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap';
    fontLink.rel = 'stylesheet';
    fontLink.media = 'print';
    fontLink.onload = function() {
        this.media = 'all';
    };
    document.head.appendChild(fontLink);

    // Preconnect para recursos externos
    const preconnects = [
        'https://www.google-analytics.com',
        'https://www.googletagmanager.com',
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com'
    ];

    preconnects.forEach(url => {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = url;
        document.head.appendChild(link);
    });
}

// Otimização de formulários
function optimizeFormPerformance() {
    // Validação assíncrona para campos pesados
    const cnpjField = document.getElementById('cnpj');
    if (cnpjField) {
        const debouncedValidation = debounce((value) => {
            // Validação CNPJ em background
            if (value.length === 18) {
                setTimeout(() => {
                    const isValid = isValidCNPJ(value.replace(/\D/g, ''));
                    cnpjField.classList.toggle('valid', isValid);
                    cnpjField.classList.toggle('invalid', !isValid);
                }, 0);
            }
        }, 500);

        cnpjField.addEventListener('input', (e) => {
            debouncedValidation(e.target.value);
        });
    }
}

// Cache de resultados da calculadora
const calculatorCache = new Map();

function getCachedCalculation(tipo, faturamento, funcionarios) {
    const key = `${tipo}-${faturamento}-${funcionarios}`;
    return calculatorCache.get(key);
}

function setCachedCalculation(tipo, faturamento, funcionarios, resultado) {
    const key = `${tipo}-${faturamento}-${funcionarios}`;
    calculatorCache.set(key, resultado);

    // Limitar cache a 50 entradas
    if (calculatorCache.size > 50) {
        const firstKey = calculatorCache.keys().next().value;
        calculatorCache.delete(firstKey);
    }
}

// Monitoramento de performance
function monitorPerformance() {
    // Web Vitals
    if ('PerformanceObserver' in window) {
        // Largest Contentful Paint
        new PerformanceObserver((entryList) => {
            for (const entry of entryList.getEntries()) {
                console.log('LCP:', entry.startTime);
            }
        }).observe({entryTypes: ['largest-contentful-paint']});

        // First Input Delay
        new PerformanceObserver((entryList) => {
            for (const entry of entryList.getEntries()) {
                console.log('FID:', entry.processingStart - entry.startTime);
            }
        }).observe({entryTypes: ['first-input']});

        // Cumulative Layout Shift
        let clsValue = 0;
        new PerformanceObserver((entryList) => {
            for (const entry of entryList.getEntries()) {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            }
            console.log('CLS:', clsValue);
        }).observe({entryTypes: ['layout-shift']});
    }

    // Navigation Timing
    window.addEventListener('load', () => {
        setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('Page Load Time:', perfData.loadEventEnd - perfData.fetchStart);
            console.log('DOM Content Loaded:', perfData.domContentLoadedEventEnd - perfData.fetchStart);
            console.log('First Paint:', performance.getEntriesByType('paint')[0]?.startTime);
        }, 0);
    });
}

// Inicialização das otimizações
function initPerformanceOptimizations() {
    // Executar otimizações críticas imediatamente
    inlineCriticalCSS();
    preloadCriticalResources();

    // Executar otimizações não críticas após o load
    window.addEventListener('load', () => {
        initLazyLoading();
        loadNonCriticalResources();
        optimizeFormPerformance();
        monitorPerformance();

        // Adicionar event listeners otimizados
        window.addEventListener('scroll', optimizedScroll, { passive: true });
        window.addEventListener('resize', optimizedResize, { passive: true });
    });
}

// Exportar funções para uso global
window.getCachedCalculation = getCachedCalculation;
window.setCachedCalculation = setCachedCalculation;

// Auto-inicializar
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPerformanceOptimizations);
} else {
    initPerformanceOptimizations();
}
