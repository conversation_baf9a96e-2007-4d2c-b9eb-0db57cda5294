# Script de Verificação - Logyc Contabilidade
# PowerShell script para verificar se a migração foi bem-sucedida

Write-Host "🔍 Verificando migração da estrutura..." -ForegroundColor Green

$errors = @()
$warnings = @()
$success = @()

# Verificar estrutura de pastas
Write-Host "`n📁 Verificando estrutura de pastas..." -ForegroundColor Cyan

$requiredFolders = @(
    "assets\css",
    "assets\js\core",
    "assets\js\components", 
    "assets\js\pages",
    "assets\images\logos",
    "docs\setup"
)

foreach ($folder in $requiredFolders) {
    if (Test-Path $folder) {
        $success += "✅ Pasta existe: $folder"
    } else {
        $errors += "❌ Pasta faltando: $folder"
    }
}

# Verificar arquivos principais
Write-Host "`n📄 Verificando arquivos principais..." -ForegroundColor Cyan

$requiredFiles = @{
    "assets\css\main.css" = "CSS principal"
    "assets\css\components.css" = "CSS de componentes"
    "assets\js\main.js" = "JavaScript principal"
    "assets\js\core\config.js" = "Configurações"
    "index.html" = "Página principal"
    "PROJECT_STRUCTURE.md" = "Documentação da estrutura"
}

foreach ($file in $requiredFiles.Keys) {
    $description = $requiredFiles[$file]
    if (Test-Path $file) {
        $success += "✅ Arquivo existe: $file ($description)"
    } else {
        $errors += "❌ Arquivo faltando: $file ($description)"
    }
}

# Verificar referências nos HTMLs
Write-Host "`n🔗 Verificando referências nos arquivos HTML..." -ForegroundColor Cyan

$htmlFiles = @(
    "index.html",
    "como-podemos-ajudar.html",
    "troca-contabilidade.html",
    "calculadora.html"
)

foreach ($file in $htmlFiles) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        
        # Verificar se ainda tem referências antigas
        if ($content -match 'href="styles\.css"') {
            $warnings += "⚠️  $file ainda referencia styles.css antigo"
        }
        
        if ($content -match 'src="script\.js"') {
            $warnings += "⚠️  $file ainda referencia script.js antigo"
        }
        
        if ($content -match 'src="calculator\.js"') {
            $warnings += "⚠️  $file ainda referencia calculator.js antigo"
        }
        
        # Verificar se tem novas referências
        if ($content -match 'assets/css/main\.css') {
            $success += "✅ $file usa nova referência CSS"
        }
        
        if ($content -match 'assets/js/main\.js') {
            $success += "✅ $file usa nova referência JS"
        }
    }
}

# Verificar tamanho dos arquivos
Write-Host "`n📊 Verificando tamanho dos arquivos..." -ForegroundColor Cyan

$filesToCheck = @(
    "assets\css\main.css",
    "assets\css\components.css", 
    "assets\js\main.js",
    "assets\js\core\config.js"
)

foreach ($file in $filesToCheck) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        if ($size -gt 0) {
            $sizeKB = [math]::Round($size / 1024, 2)
            $success += "✅ $file tem conteúdo ($sizeKB KB)"
        } else {
            $warnings += "⚠️  $file está vazio"
        }
    }
}

# Verificar arquivos antigos
Write-Host "`n🗑️ Verificando arquivos antigos..." -ForegroundColor Cyan

$oldFiles = @(
    "styles.css",
    "calculator.js",
    "faq-interactive.js", 
    "script.js",
    "config.js"
)

$oldFilesFound = @()
foreach ($file in $oldFiles) {
    if (Test-Path $file) {
        $oldFilesFound += $file
    }
}

if ($oldFilesFound.Count -gt 0) {
    $warnings += "⚠️  Arquivos antigos ainda presentes: $($oldFilesFound -join ', ')"
    $warnings += "   Considere removê-los após verificar que tudo funciona"
} else {
    $success += "✅ Nenhum arquivo antigo encontrado"
}

# Verificar imports CSS
Write-Host "`n🎨 Verificando imports CSS..." -ForegroundColor Cyan

if (Test-Path "assets\css\main.css") {
    $cssContent = Get-Content "assets\css\main.css" -Raw
    
    if ($cssContent -match "@import.*components\.css") {
        $success += "✅ main.css importa components.css"
    } else {
        $warnings += "⚠️  main.css não importa components.css"
    }
    
    if ($cssContent -match ":root") {
        $success += "✅ main.css contém variáveis CSS"
    } else {
        $warnings += "⚠️  main.css não contém variáveis CSS"
    }
}

# Verificar configurações JavaScript
Write-Host "`n⚙️ Verificando configurações JavaScript..." -ForegroundColor Cyan

if (Test-Path "assets\js\core\config.js") {
    $jsContent = Get-Content "assets\js\core\config.js" -Raw
    
    if ($jsContent -match "COMPANY_CONFIG") {
        $success += "✅ config.js contém COMPANY_CONFIG"
    } else {
        $warnings += "⚠️  config.js não contém COMPANY_CONFIG"
    }
    
    if ($jsContent -match "whatsapp") {
        $success += "✅ config.js contém configurações WhatsApp"
    } else {
        $warnings += "⚠️  config.js não contém configurações WhatsApp"
    }
}

# Exibir resultados
Write-Host "`n📋 RELATÓRIO DE VERIFICAÇÃO" -ForegroundColor Yellow
Write-Host "=" * 50 -ForegroundColor Yellow

if ($success.Count -gt 0) {
    Write-Host "`n✅ SUCESSOS ($($success.Count)):" -ForegroundColor Green
    foreach ($item in $success) {
        Write-Host "   $item" -ForegroundColor Green
    }
}

if ($warnings.Count -gt 0) {
    Write-Host "`n⚠️  AVISOS ($($warnings.Count)):" -ForegroundColor Yellow
    foreach ($item in $warnings) {
        Write-Host "   $item" -ForegroundColor Yellow
    }
}

if ($errors.Count -gt 0) {
    Write-Host "`n❌ ERROS ($($errors.Count)):" -ForegroundColor Red
    foreach ($item in $errors) {
        Write-Host "   $item" -ForegroundColor Red
    }
}

# Status final
Write-Host "`n🎯 STATUS FINAL:" -ForegroundColor Cyan

if ($errors.Count -eq 0 -and $warnings.Count -eq 0) {
    Write-Host "🎉 MIGRAÇÃO PERFEITA! Tudo está funcionando corretamente." -ForegroundColor Green
} elseif ($errors.Count -eq 0) {
    Write-Host "✅ MIGRAÇÃO BOA! Apenas alguns avisos que podem ser ignorados." -ForegroundColor Yellow
} else {
    Write-Host "⚠️  MIGRAÇÃO COM PROBLEMAS! Corrija os erros antes de continuar." -ForegroundColor Red
}

Write-Host "`n📝 PRÓXIMOS PASSOS RECOMENDADOS:" -ForegroundColor Cyan
Write-Host "   1. Abra index.html no navegador" -ForegroundColor White
Write-Host "   2. Verifique se CSS carrega corretamente" -ForegroundColor White
Write-Host "   3. Teste JavaScript (console F12)" -ForegroundColor White
Write-Host "   4. Teste calculadora e FAQ" -ForegroundColor White
Write-Host "   5. Verifique responsividade" -ForegroundColor White

if ($oldFilesFound.Count -gt 0) {
    Write-Host "`n🗑️  LIMPEZA OPCIONAL:" -ForegroundColor Cyan
    Write-Host "   Após verificar que tudo funciona, você pode remover:" -ForegroundColor White
    foreach ($file in $oldFilesFound) {
        Write-Host "   - $file" -ForegroundColor Gray
    }
}

Write-Host "`n📊 ESTATÍSTICAS:" -ForegroundColor Cyan
Write-Host "   ✅ Sucessos: $($success.Count)" -ForegroundColor Green
Write-Host "   ⚠️  Avisos: $($warnings.Count)" -ForegroundColor Yellow  
Write-Host "   ❌ Erros: $($errors.Count)" -ForegroundColor Red

pause
