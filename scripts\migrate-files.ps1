# Script de Migração - Logyc Contabilidade
# PowerShell script para organizar arquivos na nova estrutura

Write-Host "🚀 Iniciando migração para nova estrutura..." -ForegroundColor Green

# Criar estrutura de pastas se não existir
$folders = @(
    "assets\css",
    "assets\js\core",
    "assets\js\components", 
    "assets\js\pages",
    "assets\js\utils",
    "assets\js\vendor",
    "assets\images\logos",
    "assets\images\icons",
    "assets\images\backgrounds",
    "assets\images\content",
    "assets\images\optimized",
    "docs\setup",
    "docs\features",
    "docs\api",
    "docs\maintenance",
    "pages"
)

foreach ($folder in $folders) {
    if (!(Test-Path $folder)) {
        New-Item -ItemType Directory -Path $folder -Force
        Write-Host "✅ Pasta criada: $folder" -ForegroundColor Yellow
    }
}

# Mover arquivos CSS
Write-Host "`n📁 Movendo arquivos CSS..." -ForegroundColor Cyan

if (Test-Path "styles.css") {
    # O conteúdo já foi copiado para assets/css/components.css
    Write-Host "✅ styles.css -> assets/css/components.css (já migrado)" -ForegroundColor Green
}

if (Test-Path "forms.css") {
    Move-Item "forms.css" "assets\css\forms.css" -Force
    Write-Host "✅ forms.css -> assets/css/forms.css" -ForegroundColor Green
}

# Mover arquivos JavaScript
Write-Host "`n📜 Movendo arquivos JavaScript..." -ForegroundColor Cyan

$jsFiles = @{
    "calculator.js" = "assets\js\pages\calculator.js"
    "faq-interactive.js" = "assets\js\components\faq-interactive.js"
    "script.js" = "assets\js\main-old.js"
    "help-form.js" = "assets\js\pages\help-form.js"
    "switch-form.js" = "assets\js\pages\switch-form.js"
    "config.js" = "assets\js\core\config-old.js"
}

foreach ($file in $jsFiles.Keys) {
    if (Test-Path $file) {
        $destination = $jsFiles[$file]
        Move-Item $file $destination -Force
        Write-Host "✅ $file -> $destination" -ForegroundColor Green
    }
}

# Mover imagens
Write-Host "`n🖼️ Movendo imagens..." -ForegroundColor Cyan

if (Test-Path "logo.png") {
    Move-Item "logo.png" "assets\images\logos\logo.png" -Force
    Write-Host "✅ logo.png -> assets/images/logos/logo.png" -ForegroundColor Green
}

if (Test-Path "favicon.ico") {
    Move-Item "favicon.ico" "assets\images\icons\favicon.ico" -Force
    Write-Host "✅ favicon.ico -> assets/images/icons/favicon.ico" -ForegroundColor Green
}

# Mover documentação
Write-Host "`n📚 Organizando documentação..." -ForegroundColor Cyan

$docFiles = @{
    "checklist.md" = "docs\features\checklist.md"
    "migrate-structure.md" = "docs\setup\migrate-structure.md"
}

foreach ($file in $docFiles.Keys) {
    if (Test-Path $file) {
        $destination = $docFiles[$file]
        Move-Item $file $destination -Force
        Write-Host "✅ $file -> $destination" -ForegroundColor Green
    }
}

# Mover páginas HTML para pasta pages (opcional)
Write-Host "`n📄 Organizando páginas HTML..." -ForegroundColor Cyan

$htmlFiles = @(
    "como-podemos-ajudar.html",
    "troca-contabilidade.html", 
    "calculadora.html",
    "blog.html",
    "admin-dashboard.html"
)

foreach ($file in $htmlFiles) {
    if (Test-Path $file) {
        # Criar cópia na pasta pages (manter original na raiz)
        Copy-Item $file "pages\$file" -Force
        Write-Host "✅ $file -> pages/$file (cópia criada)" -ForegroundColor Green
    }
}

# Atualizar referências nos arquivos HTML
Write-Host "`n🔧 Atualizando referências nos arquivos HTML..." -ForegroundColor Cyan

$htmlFilesToUpdate = @(
    "index.html",
    "como-podemos-ajudar.html",
    "troca-contabilidade.html",
    "calculadora.html",
    "blog.html",
    "admin-dashboard.html"
)

foreach ($file in $htmlFilesToUpdate) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        
        # Atualizar referências CSS
        $content = $content -replace 'href="styles\.css"', 'href="assets/css/main.css"'
        $content = $content -replace 'href="forms\.css"', 'href="assets/css/forms.css"'
        
        # Atualizar referências JavaScript
        $content = $content -replace 'src="script\.js"', 'src="assets/js/main.js"'
        $content = $content -replace 'src="calculator\.js"', 'src="assets/js/pages/calculator.js"'
        $content = $content -replace 'src="faq-interactive\.js"', 'src="assets/js/components/faq-interactive.js"'
        $content = $content -replace 'src="config\.js"', 'src="assets/js/core/config.js"'
        
        # Atualizar referências de imagens
        $content = $content -replace 'src="logo\.png"', 'src="assets/images/logos/logo.png"'
        $content = $content -replace 'href="favicon\.ico"', 'href="assets/images/icons/favicon.ico"'
        
        Set-Content $file $content
        Write-Host "✅ Referências atualizadas em: $file" -ForegroundColor Green
    }
}

# Criar arquivo de imports CSS principal
Write-Host "`n🎨 Criando arquivo CSS principal..." -ForegroundColor Cyan

$mainCssContent = @"
/* 
 * Logyc Contabilidade - CSS Principal
 * Arquivo principal que importa todos os estilos
 */

/* Importar estilos organizados */
@import url('components.css');
@import url('forms.css');

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

/* Variáveis CSS já definidas em main.css */
"@

Set-Content "assets\css\imports.css" $mainCssContent
Write-Host "✅ Arquivo assets/css/imports.css criado" -ForegroundColor Green

# Limpar arquivos antigos (opcional - comentado por segurança)
Write-Host "`n🧹 Limpeza de arquivos antigos..." -ForegroundColor Cyan
Write-Host "⚠️  Arquivos antigos mantidos por segurança" -ForegroundColor Yellow
Write-Host "   Você pode removê-los manualmente após verificar que tudo funciona:" -ForegroundColor Yellow

$oldFiles = @(
    "styles.css",
    "calculator.js", 
    "faq-interactive.js",
    "script.js",
    "config.js"
)

foreach ($file in $oldFiles) {
    if (Test-Path $file) {
        Write-Host "   - $file" -ForegroundColor Gray
    }
}

Write-Host "`n🎉 Migração concluída com sucesso!" -ForegroundColor Green
Write-Host "📋 Próximos passos:" -ForegroundColor Cyan
Write-Host "   1. Teste todas as páginas" -ForegroundColor White
Write-Host "   2. Verifique se CSS e JS carregam corretamente" -ForegroundColor White
Write-Host "   3. Teste funcionalidades (calculadora, FAQ, formulários)" -ForegroundColor White
Write-Host "   4. Se tudo estiver funcionando, remova arquivos antigos" -ForegroundColor White
Write-Host "   5. Faça commit das mudanças" -ForegroundColor White

Write-Host "`n📁 Nova estrutura criada:" -ForegroundColor Green
Write-Host "   assets/css/ - Estilos organizados" -ForegroundColor White
Write-Host "   assets/js/ - Scripts modulares" -ForegroundColor White  
Write-Host "   assets/images/ - Imagens categorizadas" -ForegroundColor White
Write-Host "   docs/ - Documentação completa" -ForegroundColor White
Write-Host "   pages/ - Páginas HTML (cópias)" -ForegroundColor White

pause
