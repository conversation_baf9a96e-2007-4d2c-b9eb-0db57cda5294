/**
 * Logyc Contabilidade - Calculadora de Mensalidade
 * Script específico para a página da calculadora
 */

// JavaScript para a calculadora de mensalidade
let selectedType = null; // Variável global

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM carregado, inicializando calculadora...');

    const companyTypes = document.querySelectorAll('.company-type');
    const calculationInputs = document.getElementById('calculationInputs');
    const calcularBtn = document.getElementById('calcular');
    const newCalculationBtn = document.getElementById('newCalculation');
    const calculationResult = document.getElementById('calculationResult');
    const faturamentoInput = document.getElementById('faturamento');
    const funcionariosInput = document.getElementById('funcionarios');

    console.log('Elementos encontrados:');
    console.log('companyTypes:', companyTypes.length);
    console.log('calculationInputs:', calculationInputs);
    console.log('calcularBtn:', calcularBtn);
    console.log('calculationResult:', calculationResult);

    // Configurar seleção de tipo de empresa
    companyTypes.forEach(type => {
        type.addEventListener('click', function() {
            // Remove seleção anterior
            companyTypes.forEach(t => t.classList.remove('selected'));

            // Adiciona seleção atual
            this.classList.add('selected');
            selectedType = this.dataset.type;

            // Mostra inputs de cálculo
            calculationInputs.style.display = 'block';
            calculationResult.style.display = 'none';

            // Limpa campos
            document.getElementById('faturamento').value = '';
            document.getElementById('funcionarios').value = '';

            // Foca no campo de faturamento
            setTimeout(() => {
                document.getElementById('faturamento').focus();
            }, 100);
        });
    });

    // Máscara melhorada para faturamento
    if (faturamentoInput) {
        // Permitir apenas números, vírgula e ponto
        faturamentoInput.addEventListener('keypress', function(e) {
            const char = String.fromCharCode(e.which);
            if (!/[0-9.,]/.test(char) && e.which !== 8 && e.which !== 46) {
                e.preventDefault();
            }
        });

        // Formatação em tempo real mais suave
        faturamentoInput.addEventListener('input', function(e) {
            let value = e.target.value;

            // Remove caracteres não numéricos exceto vírgula e ponto
            value = value.replace(/[^\d.,]/g, '');

            // Substitui múltiplas vírgulas/pontos
            value = value.replace(/[.,]/g, function(match, offset, string) {
                return string.indexOf(match) === offset ? match : '';
            });

            // Limita a 2 casas decimais
            if (value.includes(',')) {
                const parts = value.split(',');
                if (parts[1] && parts[1].length > 2) {
                    parts[1] = parts[1].substring(0, 2);
                    value = parts.join(',');
                }
            } else if (value.includes('.')) {
                const parts = value.split('.');
                if (parts[1] && parts[1].length > 2) {
                    parts[1] = parts[1].substring(0, 2);
                    value = parts.join('.');
                }
            }

            e.target.value = value;
        });

        // Formatação final ao sair do campo
        faturamentoInput.addEventListener('blur', function(e) {
            let value = e.target.value;
            if (value && value !== '') {
                // Converte para número
                value = value.replace(',', '.');
                const numValue = parseFloat(value);

                if (!isNaN(numValue) && numValue > 0) {
                    // Formata como moeda brasileira
                    e.target.value = numValue.toLocaleString('pt-BR', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                }
            }
        });

        // Remove formatação ao focar para facilitar edição
        faturamentoInput.addEventListener('focus', function(e) {
            let value = e.target.value;
            if (value) {
                // Remove formatação de milhares, mantém apenas números e vírgula/ponto
                value = value.replace(/\./g, '').replace(',', '.');
                const numValue = parseFloat(value);
                if (!isNaN(numValue)) {
                    e.target.value = numValue.toString().replace('.', ',');
                }
            }
        });
    }

    // Validação para campo de funcionários
    if (funcionariosInput) {
        funcionariosInput.addEventListener('keypress', function(e) {
            // Permitir apenas números
            const char = String.fromCharCode(e.which);
            if (!/[0-9]/.test(char) && e.which !== 8 && e.which !== 46) {
                e.preventDefault();
            }
        });

        funcionariosInput.addEventListener('input', function(e) {
            // Remove caracteres não numéricos
            let value = e.target.value.replace(/\D/g, '');

            // Limita a 3 dígitos (máximo 999 funcionários)
            if (value.length > 3) {
                value = value.substring(0, 3);
            }

            e.target.value = value;
        });
    }

    // Botão calcular - versão simplificada
    if (calcularBtn) {
        calcularBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('=== BOTÃO CALCULAR CLICADO ===');

            try {
                // Verificar se tipo foi selecionado
                if (!selectedType) {
                    alert('Por favor, selecione o tipo da sua empresa primeiro.');
                    return;
                }

                // Obter valores
                const faturamentoValue = document.getElementById('faturamento').value;
                const funcionariosValue = document.getElementById('funcionarios').value || '0';

                console.log('Valores obtidos:');
                console.log('selectedType:', selectedType);
                console.log('faturamento:', faturamentoValue);
                console.log('funcionarios:', funcionariosValue);

                // Validação básica
                if (!faturamentoValue || faturamentoValue.trim() === '') {
                    alert('Por favor, informe o faturamento médio mensal.');
                    return;
                }

                // Converter valores
                let faturamento = parseFloat(faturamentoValue.replace(/[^\d.,]/g, '').replace(',', '.'));
                let funcionarios = parseInt(funcionariosValue) || 0;

                console.log('Valores convertidos:');
                console.log('faturamento:', faturamento);
                console.log('funcionarios:', funcionarios);

                if (isNaN(faturamento) || faturamento <= 0) {
                    alert('Por favor, informe um valor de faturamento válido.');
                    return;
                }

                // Executar cálculo
                console.log('Executando cálculo...');
                const resultado = calculateMensalidade(selectedType, faturamento, funcionarios);
                console.log('Resultado calculado:', resultado);

                // Exibir resultado
                displayResult(resultado, faturamento, funcionarios);

            } catch (error) {
                console.error('Erro no cálculo:', error);
                alert('Ocorreu um erro no cálculo. Tente novamente.');
            }
        });
        console.log('Event listener do botão calcular adicionado com sucesso');
    } else {
        console.log('ERRO: Botão calcular não encontrado!');
    }

    // Botão nova simulação
    if (newCalculationBtn) {
        newCalculationBtn.addEventListener('click', function() {
            resetCalculator();
        });
    }

    // Enter para calcular
    if (calculationInputs) {
        calculationInputs.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                if (validateCalculatorInputs()) {
                    performCalculation();
                }
            }
        });
    }
});

function validateCalculatorInputs() {
    const faturamento = document.getElementById('faturamento').value;
    const funcionarios = document.getElementById('funcionarios').value;

    console.log('Validando inputs...');
    console.log('selectedType na validação:', selectedType);
    console.log('faturamento na validação:', faturamento);
    console.log('funcionarios na validação:', funcionarios);

    if (!selectedType) {
        console.log('Erro: Tipo não selecionado');
        showCalculatorError('Por favor, selecione o tipo da sua empresa.');
        return false;
    }

    if (!faturamento || faturamento.trim() === '' || faturamento === '0,00' || faturamento === '0') {
        showCalculatorError('Por favor, informe o faturamento médio mensal.');
        document.getElementById('faturamento').focus();
        return false;
    }

    // Melhor parsing do valor
    let faturamentoValue;
    if (faturamento.includes(',')) {
        faturamentoValue = parseFloat(faturamento.replace(/\./g, '').replace(',', '.'));
    } else {
        faturamentoValue = parseFloat(faturamento.replace(/[^\d.]/g, ''));
    }

    if (isNaN(faturamentoValue) || faturamentoValue <= 0) {
        showCalculatorError('Por favor, informe um valor de faturamento válido maior que zero.');
        document.getElementById('faturamento').focus();
        return false;
    }

    const funcionariosValue = parseInt(funcionarios) || 0;
    if (funcionariosValue < 0) {
        showCalculatorError('O número de funcionários não pode ser negativo.');
        document.getElementById('funcionarios').focus();
        return false;
    }

    return true;
}

function performCalculation() {
    const faturamento = document.getElementById('faturamento').value;
    const funcionarios = parseInt(document.getElementById('funcionarios').value) || 0;

    // Usar o mesmo parsing da validação
    let faturamentoValue;
    if (faturamento.includes(',')) {
        faturamentoValue = parseFloat(faturamento.replace(/\./g, '').replace(',', '.'));
    } else {
        faturamentoValue = parseFloat(faturamento.replace(/[^\d.]/g, ''));
    }

    const resultado = calculateMensalidade(selectedType, faturamentoValue, funcionarios);

    displayResult(resultado, faturamentoValue, funcionarios);
}

function calculateMensalidade(tipo, faturamento, funcionarios) {
    let mensalidadeBase = 0;
    let custoFuncionarios = 0;
    let breakdown = '';
    let limite = '';

    switch (tipo) {
        case 'mei':
            mensalidadeBase = 75;
            custoFuncionarios = funcionarios * 40;
            limite = 'Limite: R$ 81.000,00 anuais (R$ 6.750,00 mensais)';
            breakdown = `Mensalidade MEI: R$ 75,00\nFuncionários (${funcionarios} x R$ 40,00): R$ ${custoFuncionarios.toFixed(2)}`;

            if (faturamento > 6750) {
                breakdown += '\n⚠️ Atenção: Faturamento acima do limite MEI!';
            }
            break;

        case 'servicos':
            if (faturamento <= 20000) {
                mensalidadeBase = 285;
            } else {
                mensalidadeBase = 370;
            }

            const funcionariosExtras = Math.max(0, funcionarios - 3);
            custoFuncionarios = funcionariosExtras * 25;

            breakdown = `Mensalidade base: R$ ${mensalidadeBase.toFixed(2)}\nFuncionários inclusos: até 3\nFuncionários extras (${funcionariosExtras} x R$ 25,00): R$ ${custoFuncionarios.toFixed(2)}`;
            break;

        case 'comercio':
            if (faturamento <= 25000) {
                mensalidadeBase = 245;
            } else {
                mensalidadeBase = 390;
            }

            const funcionariosExtrasComercio = Math.max(0, funcionarios - 3);
            custoFuncionarios = funcionariosExtrasComercio * 25;

            breakdown = `Mensalidade base: R$ ${mensalidadeBase.toFixed(2)}\nFuncionários inclusos: até 3\nFuncionários extras (${funcionariosExtrasComercio} x R$ 25,00): R$ ${custoFuncionarios.toFixed(2)}`;
            break;

        case 'industria':
            if (faturamento <= 35000) {
                mensalidadeBase = 300;
            } else {
                mensalidadeBase = 545;
            }

            const funcionariosExtrasIndustria = Math.max(0, funcionarios - 3);
            custoFuncionarios = funcionariosExtrasIndustria * 25;

            breakdown = `Mensalidade base: R$ ${mensalidadeBase.toFixed(2)}\nFuncionários inclusos: até 3\nFuncionários extras (${funcionariosExtrasIndustria} x R$ 25,00): R$ ${custoFuncionarios.toFixed(2)}`;
            break;

        case 'profissionais':
            if (faturamento <= 15000) {
                mensalidadeBase = 199;
            } else {
                mensalidadeBase = 234;
            }

            const funcionariosExtrasProfissionais = Math.max(0, funcionarios - 3);
            custoFuncionarios = funcionariosExtrasProfissionais * 25;

            breakdown = `Mensalidade base: R$ ${mensalidadeBase.toFixed(2)}\nFuncionários inclusos: até 3\nFuncionários extras (${funcionariosExtrasProfissionais} x R$ 25,00): R$ ${custoFuncionarios.toFixed(2)}`;
            break;
    }

    const total = mensalidadeBase + custoFuncionarios;

    return {
        tipo,
        mensalidadeBase,
        custoFuncionarios,
        total,
        breakdown,
        limite
    };
}
