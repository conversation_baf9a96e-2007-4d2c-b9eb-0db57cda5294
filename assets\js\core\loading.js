// Sistema de Loading States para Logyc Contabilidade
class LoadingManager {
    constructor() {
        this.activeLoaders = new Set();
        this.init();
    }
    
    init() {
        this.createStyles();
        this.setupGlobalLoader();
    }
    
    createStyles() {
        const styles = `
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .loading-overlay.show {
                opacity: 1;
                visibility: visible;
            }
            
            .loading-spinner {
                width: 50px;
                height: 50px;
                border: 4px solid rgba(255, 255, 255, 0.3);
                border-top: 4px solid #005aec;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            
            .loading-content {
                text-align: center;
                color: white;
            }
            
            .loading-text {
                margin-top: 16px;
                font-size: 16px;
                font-weight: 500;
            }
            
            .loading-subtext {
                margin-top: 8px;
                font-size: 14px;
                opacity: 0.8;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            /* Button loading states */
            .btn-loading {
                position: relative;
                pointer-events: none;
                opacity: 0.7;
            }
            
            .btn-loading::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 16px;
                height: 16px;
                margin: -8px 0 0 -8px;
                border: 2px solid transparent;
                border-top: 2px solid currentColor;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            
            .btn-loading .btn-text {
                opacity: 0;
            }
            
            /* Form loading states */
            .form-loading {
                position: relative;
                pointer-events: none;
            }
            
            .form-loading::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.8);
                z-index: 10;
            }
            
            .form-loading::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 30px;
                height: 30px;
                margin: -15px 0 0 -15px;
                border: 3px solid rgba(0, 90, 236, 0.3);
                border-top: 3px solid #005aec;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                z-index: 11;
            }
            
            /* Skeleton loading */
            .skeleton {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: skeleton-loading 1.5s infinite;
            }
            
            @keyframes skeleton-loading {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }
            
            .skeleton-text {
                height: 16px;
                border-radius: 4px;
                margin-bottom: 8px;
            }
            
            .skeleton-text.short {
                width: 60%;
            }
            
            .skeleton-text.medium {
                width: 80%;
            }
            
            .skeleton-text.long {
                width: 100%;
            }
            
            /* Progress bar */
            .progress-bar {
                width: 100%;
                height: 4px;
                background: #f0f0f0;
                border-radius: 2px;
                overflow: hidden;
                margin: 16px 0;
            }
            
            .progress-fill {
                height: 100%;
                background: #005aec;
                border-radius: 2px;
                transition: width 0.3s ease;
                width: 0%;
            }
            
            .progress-indeterminate .progress-fill {
                width: 30%;
                animation: progress-indeterminate 2s infinite;
            }
            
            @keyframes progress-indeterminate {
                0% { transform: translateX(-100%); }
                100% { transform: translateX(400%); }
            }
            
            /* Pulse loading */
            .pulse-loading {
                animation: pulse 1.5s ease-in-out infinite;
            }
            
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }
    
    setupGlobalLoader() {
        this.globalLoader = document.createElement('div');
        this.globalLoader.className = 'loading-overlay';
        this.globalLoader.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">Carregando...</div>
                <div class="loading-subtext">Aguarde um momento</div>
            </div>
        `;
        document.body.appendChild(this.globalLoader);
    }
    
    // Mostrar loading global
    showGlobal(text = 'Carregando...', subtext = 'Aguarde um momento') {
        const textElement = this.globalLoader.querySelector('.loading-text');
        const subtextElement = this.globalLoader.querySelector('.loading-subtext');
        
        if (textElement) textElement.textContent = text;
        if (subtextElement) subtextElement.textContent = subtext;
        
        this.globalLoader.classList.add('show');
        document.body.style.overflow = 'hidden';
        
        return 'global';
    }
    
    // Esconder loading global
    hideGlobal() {
        this.globalLoader.classList.remove('show');
        document.body.style.overflow = '';
    }
    
    // Loading para botões
    showButtonLoading(button, text = '') {
        if (typeof button === 'string') {
            button = document.querySelector(button);
        }
        
        if (!button) return;
        
        const originalText = button.textContent;
        button.setAttribute('data-original-text', originalText);
        button.classList.add('btn-loading');
        
        if (text) {
            button.innerHTML = `<span class="btn-text">${text}</span>`;
        }
        
        const loaderId = `btn-${Date.now()}`;
        button.setAttribute('data-loader-id', loaderId);
        this.activeLoaders.add(loaderId);
        
        return loaderId;
    }
    
    hideButtonLoading(button) {
        if (typeof button === 'string') {
            button = document.querySelector(button);
        }
        
        if (!button) return;
        
        const originalText = button.getAttribute('data-original-text');
        const loaderId = button.getAttribute('data-loader-id');
        
        button.classList.remove('btn-loading');
        button.textContent = originalText;
        button.removeAttribute('data-original-text');
        button.removeAttribute('data-loader-id');
        
        if (loaderId) {
            this.activeLoaders.delete(loaderId);
        }
    }
    
    // Loading para formulários
    showFormLoading(form) {
        if (typeof form === 'string') {
            form = document.querySelector(form);
        }
        
        if (!form) return;
        
        form.classList.add('form-loading');
        
        const loaderId = `form-${Date.now()}`;
        form.setAttribute('data-loader-id', loaderId);
        this.activeLoaders.add(loaderId);
        
        return loaderId;
    }
    
    hideFormLoading(form) {
        if (typeof form === 'string') {
            form = document.querySelector(form);
        }
        
        if (!form) return;
        
        const loaderId = form.getAttribute('data-loader-id');
        
        form.classList.remove('form-loading');
        form.removeAttribute('data-loader-id');
        
        if (loaderId) {
            this.activeLoaders.delete(loaderId);
        }
    }
    
    // Progress bar
    createProgressBar(container, options = {}) {
        const {
            indeterminate = false,
            value = 0,
            max = 100
        } = options;
        
        const progressBar = document.createElement('div');
        progressBar.className = `progress-bar ${indeterminate ? 'progress-indeterminate' : ''}`;
        
        const progressFill = document.createElement('div');
        progressFill.className = 'progress-fill';
        
        if (!indeterminate) {
            progressFill.style.width = `${(value / max) * 100}%`;
        }
        
        progressBar.appendChild(progressFill);
        
        if (typeof container === 'string') {
            container = document.querySelector(container);
        }
        
        if (container) {
            container.appendChild(progressBar);
        }
        
        return {
            element: progressBar,
            update: (newValue) => {
                if (!indeterminate) {
                    progressFill.style.width = `${(newValue / max) * 100}%`;
                }
            },
            remove: () => {
                if (progressBar.parentNode) {
                    progressBar.parentNode.removeChild(progressBar);
                }
            }
        };
    }
    
    // Skeleton loading
    createSkeleton(container, lines = 3) {
        const skeleton = document.createElement('div');
        skeleton.className = 'skeleton-container';
        
        for (let i = 0; i < lines; i++) {
            const line = document.createElement('div');
            line.className = 'skeleton skeleton-text';
            
            // Variar tamanhos
            if (i === lines - 1) {
                line.classList.add('short');
            } else if (i % 2 === 0) {
                line.classList.add('long');
            } else {
                line.classList.add('medium');
            }
            
            skeleton.appendChild(line);
        }
        
        if (typeof container === 'string') {
            container = document.querySelector(container);
        }
        
        if (container) {
            container.appendChild(skeleton);
        }
        
        return {
            element: skeleton,
            remove: () => {
                if (skeleton.parentNode) {
                    skeleton.parentNode.removeChild(skeleton);
                }
            }
        };
    }
    
    // Simular loading com delay
    async simulateLoading(duration = 2000, callback) {
        const loaderId = this.showGlobal('Processando...', 'Isso pode levar alguns segundos');
        
        return new Promise((resolve) => {
            setTimeout(() => {
                this.hideGlobal();
                if (callback) callback();
                resolve();
            }, duration);
        });
    }
    
    // Limpar todos os loadings
    clearAll() {
        this.hideGlobal();
        
        // Limpar botões
        document.querySelectorAll('.btn-loading').forEach(btn => {
            this.hideButtonLoading(btn);
        });
        
        // Limpar formulários
        document.querySelectorAll('.form-loading').forEach(form => {
            this.hideFormLoading(form);
        });
        
        this.activeLoaders.clear();
    }
}

// Criar instância global
window.loading = new LoadingManager();

// Integração com formulários existentes
document.addEventListener('DOMContentLoaded', function() {
    // Interceptar submissões de formulário
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitButton = this.querySelector('button[type="submit"], input[type="submit"]');
            if (submitButton) {
                loading.showButtonLoading(submitButton, 'Enviando...');
                
                // Simular delay para demonstração
                setTimeout(() => {
                    loading.hideButtonLoading(submitButton);
                }, 3000);
            }
        });
    });
    
    // Loading para calculadora
    const calcularBtn = document.getElementById('calcular');
    if (calcularBtn) {
        calcularBtn.addEventListener('click', function() {
            loading.showButtonLoading(this, 'Calculando...');
            
            setTimeout(() => {
                loading.hideButtonLoading(this);
            }, 1500);
        });
    }
    
    // Loading inicial da página
    window.addEventListener('load', function() {
        // Esconder qualquer loading inicial
        const pageLoader = document.querySelector('.page-loader');
        if (pageLoader) {
            pageLoader.style.display = 'none';
        }
    });
});

// Interceptar navegação para mostrar loading
let navigationTimeout;
window.addEventListener('beforeunload', function() {
    clearTimeout(navigationTimeout);
    navigationTimeout = setTimeout(() => {
        loading.showGlobal('Carregando página...', 'Redirecionando...');
    }, 100);
});

// Exportar para uso global
window.LoadingManager = LoadingManager;
