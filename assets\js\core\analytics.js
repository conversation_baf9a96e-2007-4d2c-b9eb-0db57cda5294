// Google Analytics 4 Configuration
// Substitua 'GA_MEASUREMENT_ID' pelo seu ID real do Google Analytics

// Configuração do Google Analytics 4
window.dataLayer = window.dataLayer || [];
function gtag(){dataLayer.push(arguments);}

// Inicialização básica (descomente quando tiver o ID)
/*
gtag('js', new Date());
gtag('config', 'GA_MEASUREMENT_ID', {
    page_title: document.title,
    page_location: window.location.href
});
*/

// Função para rastrear eventos personalizados
function trackEvent(eventName, parameters = {}) {
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, parameters);
    }
    console.log('Event tracked:', eventName, parameters);
}

// Rastreamento de formulários
function trackFormSubmission(formName, formData = {}) {
    trackEvent('form_submit', {
        form_name: formName,
        ...formData
    });
}

// Rastreamento de cliques em botões importantes
function trackButtonClick(buttonName, buttonLocation = '') {
    trackEvent('button_click', {
        button_name: buttonName,
        button_location: buttonLocation
    });
}

// Rastreamento de uso da calculadora
function trackCalculatorUsage(companyType, faturamento, funcionarios, resultado) {
    trackEvent('calculator_usage', {
        company_type: companyType,
        monthly_revenue: faturamento,
        employees: funcionarios,
        calculated_fee: resultado
    });
}

// Rastreamento de downloads (para futuro)
function trackDownload(fileName, fileType = '') {
    trackEvent('file_download', {
        file_name: fileName,
        file_type: fileType
    });
}

// Rastreamento de tempo na página
let pageStartTime = Date.now();

window.addEventListener('beforeunload', function() {
    const timeOnPage = Math.round((Date.now() - pageStartTime) / 1000);
    trackEvent('page_view_duration', {
        duration_seconds: timeOnPage,
        page_title: document.title
    });
});

// Rastreamento de scroll depth
let maxScrollDepth = 0;

window.addEventListener('scroll', function() {
    const scrollDepth = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
    
    if (scrollDepth > maxScrollDepth) {
        maxScrollDepth = scrollDepth;
        
        // Rastrear marcos de scroll
        if (scrollDepth >= 25 && scrollDepth < 50 && maxScrollDepth < 50) {
            trackEvent('scroll_depth', { depth: '25%' });
        } else if (scrollDepth >= 50 && scrollDepth < 75 && maxScrollDepth < 75) {
            trackEvent('scroll_depth', { depth: '50%' });
        } else if (scrollDepth >= 75 && scrollDepth < 90 && maxScrollDepth < 90) {
            trackEvent('scroll_depth', { depth: '75%' });
        } else if (scrollDepth >= 90 && maxScrollDepth < 100) {
            trackEvent('scroll_depth', { depth: '90%' });
        }
    }
});

// Rastreamento de cliques externos
document.addEventListener('click', function(e) {
    const link = e.target.closest('a');
    if (link && link.hostname !== window.location.hostname) {
        trackEvent('external_link_click', {
            link_url: link.href,
            link_text: link.textContent.trim()
        });
    }
});

// Rastreamento de erros JavaScript
window.addEventListener('error', function(e) {
    trackEvent('javascript_error', {
        error_message: e.message,
        error_filename: e.filename,
        error_line: e.lineno
    });
});

// Rastreamento de performance (Web Vitals)
function trackWebVitals() {
    // Largest Contentful Paint (LCP)
    new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
            trackEvent('web_vitals', {
                metric_name: 'LCP',
                metric_value: Math.round(entry.startTime)
            });
        }
    }).observe({entryTypes: ['largest-contentful-paint']});

    // First Input Delay (FID)
    new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
            trackEvent('web_vitals', {
                metric_name: 'FID',
                metric_value: Math.round(entry.processingStart - entry.startTime)
            });
        }
    }).observe({entryTypes: ['first-input']});

    // Cumulative Layout Shift (CLS)
    let clsValue = 0;
    new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
            if (!entry.hadRecentInput) {
                clsValue += entry.value;
            }
        }
        trackEvent('web_vitals', {
            metric_name: 'CLS',
            metric_value: Math.round(clsValue * 1000) / 1000
        });
    }).observe({entryTypes: ['layout-shift']});
}

// Inicializar rastreamento de Web Vitals
if (typeof PerformanceObserver !== 'undefined') {
    trackWebVitals();
}

// Exportar funções para uso global
window.trackEvent = trackEvent;
window.trackFormSubmission = trackFormSubmission;
window.trackButtonClick = trackButtonClick;
window.trackCalculatorUsage = trackCalculatorUsage;
window.trackDownload = trackDownload;
