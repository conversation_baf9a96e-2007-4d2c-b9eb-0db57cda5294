// Dashboard Administrativo - Logyc Contabilidade
class AdminDashboard {
    constructor() {
        this.data = {
            analytics: null,
            leads: [],
            campaigns: [],
            abTests: [],
            realTimeMetrics: {}
        };
        this.refreshInterval = null;
        this.init();
    }
    
    init() {
        this.setupNavigation();
        this.loadInitialData();
        this.startRealTimeUpdates();
        this.setupEventListeners();
    }
    
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        const tabContents = document.querySelectorAll('.tab-content');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Remove active class from all links and tabs
                navLinks.forEach(l => l.classList.remove('active'));
                tabContents.forEach(t => t.classList.remove('active'));
                
                // Add active class to clicked link
                link.classList.add('active');
                
                // Show corresponding tab
                const targetTab = link.getAttribute('href').substring(1);
                const targetContent = document.getElementById(targetTab);
                if (targetContent) {
                    targetContent.classList.add('active');
                    this.loadTabData(targetTab);
                }
            });
        });
    }
    
    loadInitialData() {
        this.loadOverviewData();
        this.loadAnalyticsData();
        this.loadLeadsData();
        this.loadCampaignsData();
        this.loadABTestsData();
        this.loadSettingsData();
    }
    
    loadOverviewData() {
        // Simulate real-time metrics
        const metrics = this.generateMetrics();
        
        document.getElementById('visitorsToday').textContent = metrics.visitors;
        document.getElementById('leadsToday').textContent = metrics.leads;
        document.getElementById('conversionRate').textContent = metrics.conversionRate + '%';
        document.getElementById('calculatorUsage').textContent = metrics.calculatorUsage;
        
        this.loadRecentLeads();
        this.loadRealTimeChart();
    }
    
    generateMetrics() {
        // Get data from analytics systems
        const analytics = window.advancedAnalytics?.getSessionSummary() || {};
        const marketing = window.marketingAutomation?.getAnalytics() || {};
        
        return {
            visitors: this.getRandomMetric(45, 120),
            leads: marketing.total_leads || this.getRandomMetric(8, 25),
            conversionRate: marketing.conversion_rate || this.getRandomMetric(12, 28),
            calculatorUsage: this.getRandomMetric(15, 45)
        };
    }
    
    getRandomMetric(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    
    loadRecentLeads() {
        const recentLeadsContainer = document.getElementById('recentLeads');
        
        // Get leads from marketing automation
        let leads = [];
        if (window.marketingAutomation) {
            // Get all leads and sort by creation date
            const allLeads = Array.from(window.marketingAutomation.leads.values());
            leads = allLeads.sort((a, b) => new Date(b.created_at) - new Date(a.created_at)).slice(0, 5);
        }
        
        if (leads.length === 0) {
            // Generate sample leads for demo
            leads = this.generateSampleLeads();
        }
        
        const leadsHTML = leads.map(lead => `
            <tr>
                <td>${lead.data?.name || 'Lead'}</td>
                <td>${this.getSourceLabel(lead.source)}</td>
                <td>${lead.score}</td>
                <td><span class="status-badge status-${this.getLeadStatus(lead.score)}">${this.getLeadStatusLabel(lead.score)}</span></td>
                <td>
                    <button class="btn-admin" onclick="viewLead('${lead.id}')">Ver</button>
                    <button class="btn-admin secondary" onclick="contactLead('${lead.id}')">Contatar</button>
                </td>
            </tr>
        `).join('');
        
        recentLeadsContainer.innerHTML = leadsHTML;
    }
    
    generateSampleLeads() {
        const sampleNames = ['João Silva', 'Maria Santos', 'Carlos Oliveira', 'Ana Costa', 'Roberto Lima'];
        const sources = ['calculator', 'form_submission', 'blog', 'whatsapp', 'organic'];
        
        return sampleNames.map((name, index) => ({
            id: `sample_${index}`,
            data: { name: name },
            source: sources[index % sources.length],
            score: Math.floor(Math.random() * 100),
            created_at: new Date(Date.now() - Math.random() * 86400000).toISOString()
        }));
    }
    
    getSourceLabel(source) {
        const labels = {
            'calculator': 'Calculadora',
            'form_submission': 'Formulário',
            'blog': 'Blog',
            'whatsapp': 'WhatsApp',
            'organic': 'Orgânico',
            'social': 'Redes Sociais'
        };
        return labels[source] || source;
    }
    
    getLeadStatus(score) {
        if (score >= 70) return 'hot';
        if (score >= 40) return 'warm';
        return 'cold';
    }
    
    getLeadStatusLabel(score) {
        if (score >= 70) return 'Quente';
        if (score >= 40) return 'Morno';
        return 'Frio';
    }
    
    loadRealTimeChart() {
        const chartContainer = document.getElementById('realTimeChart');
        
        // Simple text-based chart for demo
        const chartHTML = `
            <div style="padding: 1rem;">
                <h4>Visitantes nas Últimas 24h</h4>
                <div style="display: flex; align-items: end; gap: 4px; height: 200px; margin-top: 1rem;">
                    ${this.generateChartBars()}
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 0.5rem; font-size: 0.8rem; color: #666;">
                    <span>00:00</span>
                    <span>06:00</span>
                    <span>12:00</span>
                    <span>18:00</span>
                    <span>24:00</span>
                </div>
            </div>
        `;
        
        chartContainer.innerHTML = chartHTML;
    }
    
    generateChartBars() {
        const bars = [];
        for (let i = 0; i < 24; i++) {
            const height = Math.random() * 180 + 20;
            bars.push(`<div style="background: #005aec; width: 8px; height: ${height}px; border-radius: 2px;"></div>`);
        }
        return bars.join('');
    }
    
    loadAnalyticsData() {
        this.loadConversionFunnel();
        this.loadHeatmapData();
    }
    
    loadConversionFunnel() {
        const funnelContainer = document.getElementById('conversionFunnel');
        
        const funnelData = [
            { stage: 'Visitantes', count: 1000, percentage: 100 },
            { stage: 'Calculadora', count: 350, percentage: 35 },
            { stage: 'Formulário Iniciado', count: 120, percentage: 12 },
            { stage: 'Formulário Completo', count: 85, percentage: 8.5 },
            { stage: 'WhatsApp', count: 45, percentage: 4.5 }
        ];
        
        const funnelHTML = funnelData.map(stage => `
            <div style="margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 6px;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="font-weight: 600;">${stage.stage}</span>
                    <span style="color: #005aec; font-weight: bold;">${stage.count}</span>
                </div>
                <div style="margin-top: 0.5rem;">
                    <div style="background: #e9ecef; height: 8px; border-radius: 4px;">
                        <div style="background: #005aec; height: 100%; width: ${stage.percentage}%; border-radius: 4px;"></div>
                    </div>
                    <span style="font-size: 0.8rem; color: #666;">${stage.percentage}% dos visitantes</span>
                </div>
            </div>
        `).join('');
        
        funnelContainer.innerHTML = funnelHTML;
    }
    
    loadHeatmapData() {
        const heatmapContainer = document.getElementById('heatmapData');
        
        // Get heatmap data from analytics
        let heatmapData = [];
        if (window.advancedAnalytics?.heatmapData) {
            heatmapData = window.advancedAnalytics.heatmapData.slice(0, 10);
        }
        
        if (heatmapData.length === 0) {
            heatmapData = this.generateSampleHeatmapData();
        }
        
        const heatmapHTML = `
            <div style="padding: 1rem;">
                <h4>Elementos Mais Clicados</h4>
                ${heatmapData.map((item, index) => `
                    <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #eee;">
                        <span>${item.element || 'Elemento'} ${index + 1}</span>
                        <span style="color: #005aec; font-weight: bold;">${item.clicks || Math.floor(Math.random() * 50) + 10} cliques</span>
                    </div>
                `).join('')}
            </div>
        `;
        
        heatmapContainer.innerHTML = heatmapHTML;
    }
    
    generateSampleHeatmapData() {
        return [
            { element: 'Botão Calculadora', clicks: 45 },
            { element: 'WhatsApp', clicks: 38 },
            { element: 'Formulário Contato', clicks: 32 },
            { element: 'Menu Serviços', clicks: 28 },
            { element: 'Botão Trocar', clicks: 25 }
        ];
    }
    
    loadLeadsData() {
        const allLeadsContainer = document.getElementById('allLeads');
        
        // Get all leads
        let leads = [];
        if (window.marketingAutomation) {
            leads = Array.from(window.marketingAutomation.leads.values());
        }
        
        if (leads.length === 0) {
            leads = this.generateExtendedSampleLeads();
        }
        
        const leadsHTML = leads.map(lead => `
            <tr>
                <td>${lead.id.substring(0, 8)}...</td>
                <td>${lead.data?.name || 'N/A'}</td>
                <td>${lead.data?.email || 'N/A'}</td>
                <td>${lead.data?.company || 'N/A'}</td>
                <td>${this.getSourceLabel(lead.source)}</td>
                <td>${lead.score}</td>
                <td>${lead.stage}</td>
                <td>${new Date(lead.created_at).toLocaleDateString('pt-BR')}</td>
                <td>
                    <button class="btn-admin" onclick="viewLead('${lead.id}')">Ver</button>
                </td>
            </tr>
        `).join('');
        
        allLeadsContainer.innerHTML = leadsHTML || '<tr><td colspan="9">Nenhum lead encontrado</td></tr>';
    }
    
    generateExtendedSampleLeads() {
        const sampleData = [
            { name: 'João Silva', email: '<EMAIL>', company: 'Silva LTDA' },
            { name: 'Maria Santos', email: '<EMAIL>', company: 'Santos Comércio' },
            { name: 'Carlos Oliveira', email: '<EMAIL>', company: 'Oliveira Consultoria' },
            { name: 'Ana Costa', email: '<EMAIL>', company: 'Boutique Ana' },
            { name: 'Roberto Lima', email: '<EMAIL>', company: 'Lima Transportes' }
        ];
        
        const sources = ['calculator', 'form_submission', 'blog', 'whatsapp', 'organic'];
        const stages = ['awareness', 'consideration', 'decision'];
        
        return sampleData.map((data, index) => ({
            id: `extended_sample_${index}`,
            data: data,
            source: sources[index % sources.length],
            score: Math.floor(Math.random() * 100),
            stage: stages[Math.floor(Math.random() * stages.length)],
            created_at: new Date(Date.now() - Math.random() * 7 * 86400000).toISOString()
        }));
    }
    
    loadCampaignsData() {
        const campaignsContainer = document.getElementById('activeCampaigns');
        const emailContainer = document.getElementById('emailPerformance');
        
        // Get marketing data
        const marketingData = window.marketingAutomation?.getAnalytics() || {};
        
        const campaignsHTML = `
            <div style="padding: 1rem;">
                <div style="margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 6px;">
                    <h4>Sequência de Boas-vindas</h4>
                    <p>Status: <span style="color: #28a745; font-weight: bold;">Ativa</span></p>
                    <p>Leads inscritos: ${marketingData.total_leads || 15}</p>
                </div>
                <div style="margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 6px;">
                    <h4>Abandono da Calculadora</h4>
                    <p>Status: <span style="color: #28a745; font-weight: bold;">Ativa</span></p>
                    <p>Triggers hoje: ${Math.floor(Math.random() * 10) + 5}</p>
                </div>
            </div>
        `;
        
        const emailHTML = `
            <div style="padding: 1rem;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div style="text-align: center;">
                        <div style="font-size: 2rem; font-weight: bold; color: #005aec;">72%</div>
                        <div style="color: #666;">Taxa de Abertura</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 2rem; font-weight: bold; color: #005aec;">18%</div>
                        <div style="color: #666;">Taxa de Clique</div>
                    </div>
                </div>
                <div style="margin-top: 1rem;">
                    <p><strong>Emails enviados hoje:</strong> ${marketingData.total_emails || 25}</p>
                    <p><strong>Melhor horário:</strong> 14:00 - 16:00</p>
                </div>
            </div>
        `;
        
        campaignsContainer.innerHTML = campaignsHTML;
        emailContainer.innerHTML = emailHTML;
    }
    
    loadABTestsData() {
        const abTestsContainer = document.getElementById('abTestResults');
        
        // Get A/B test data
        let testResults = {};
        if (window.abTesting) {
            testResults = window.abTesting.getTestResults();
        }
        
        const testsHTML = Object.keys(testResults).length > 0 ? 
            Object.entries(testResults).map(([testId, test]) => `
                <div style="margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 6px;">
                    <h4>${test.test_name}</h4>
                    <p><strong>Sua variante:</strong> ${test.user_variant || 'Não atribuída'}</p>
                    <p><strong>Status:</strong> <span style="color: #28a745;">${test.test_status}</span></p>
                    <p>${test.description}</p>
                </div>
            `).join('') :
            `
                <div style="padding: 1rem;">
                    <div style="margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 6px;">
                        <h4>Hero CTA Button Test</h4>
                        <p><strong>Variante A:</strong> 52% conversão (+15%)</p>
                        <p><strong>Variante B:</strong> 45% conversão (controle)</p>
                        <p><strong>Status:</strong> <span style="color: #28a745;">Ativo</span></p>
                    </div>
                    <div style="margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 6px;">
                        <h4>Calculator Position Test</h4>
                        <p><strong>Variante A:</strong> 38% uso (+22%)</p>
                        <p><strong>Variante B:</strong> 31% uso (controle)</p>
                        <p><strong>Status:</strong> <span style="color: #28a745;">Ativo</span></p>
                    </div>
                </div>
            `;
        
        abTestsContainer.innerHTML = testsHTML;
    }
    
    loadSettingsData() {
        const settingsContainer = document.getElementById('systemSettings');
        
        const settingsHTML = `
            <div style="padding: 1rem;">
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Analytics Tracking</label>
                    <label style="display: flex; align-items: center; gap: 0.5rem;">
                        <input type="checkbox" checked> Heatmap Tracking
                    </label>
                    <label style="display: flex; align-items: center; gap: 0.5rem;">
                        <input type="checkbox" checked> Conversion Tracking
                    </label>
                    <label style="display: flex; align-items: center; gap: 0.5rem;">
                        <input type="checkbox" checked> A/B Testing
                    </label>
                </div>
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Marketing Automation</label>
                    <label style="display: flex; align-items: center; gap: 0.5rem;">
                        <input type="checkbox" checked> Email Sequences
                    </label>
                    <label style="display: flex; align-items: center; gap: 0.5rem;">
                        <input type="checkbox" checked> Lead Scoring
                    </label>
                    <label style="display: flex; align-items: center; gap: 0.5rem;">
                        <input type="checkbox" checked> Behavioral Triggers
                    </label>
                </div>
                <button class="btn-admin" onclick="saveSettings()">Salvar Configurações</button>
            </div>
        `;
        
        settingsContainer.innerHTML = settingsHTML;
    }
    
    loadTabData(tabName) {
        switch (tabName) {
            case 'overview':
                this.loadOverviewData();
                break;
            case 'analytics':
                this.loadAnalyticsData();
                break;
            case 'leads':
                this.loadLeadsData();
                break;
            case 'campaigns':
                this.loadCampaignsData();
                break;
            case 'ab-tests':
                this.loadABTestsData();
                break;
            case 'settings':
                this.loadSettingsData();
                break;
        }
    }
    
    startRealTimeUpdates() {
        // Update metrics every 30 seconds
        this.refreshInterval = setInterval(() => {
            this.loadOverviewData();
        }, 30000);
    }
    
    setupEventListeners() {
        // Add any additional event listeners here
        window.addEventListener('beforeunload', () => {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
            }
        });
    }
    
    // Public methods for button actions
    exportLeads() {
        const leads = Array.from(window.marketingAutomation?.leads.values() || []);
        const csv = this.convertToCSV(leads);
        this.downloadCSV(csv, 'leads.csv');
    }
    
    exportAllData() {
        const data = {
            analytics: window.advancedAnalytics?.exportData() || {},
            marketing: window.marketingAutomation?.getAnalytics() || {},
            abTests: window.abTesting?.getTestResults() || {},
            timestamp: new Date().toISOString()
        };
        
        const json = JSON.stringify(data, null, 2);
        this.downloadJSON(json, 'logyc_data_export.json');
    }
    
    convertToCSV(data) {
        if (!data.length) return '';
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => 
                JSON.stringify(row[header] || '')
            ).join(','))
        ].join('\n');
        
        return csvContent;
    }
    
    downloadCSV(csv, filename) {
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        window.URL.revokeObjectURL(url);
    }
    
    downloadJSON(json, filename) {
        const blob = new Blob([json], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        window.URL.revokeObjectURL(url);
    }
}

// Global functions for button actions
window.viewLead = function(leadId) {
    const lead = window.marketingAutomation?.getLeadById(leadId);
    if (lead) {
        alert(`Lead: ${lead.data?.name || 'N/A'}\nScore: ${lead.score}\nFonte: ${lead.source}\nCriado: ${new Date(lead.created_at).toLocaleString('pt-BR')}`);
    } else {
        alert('Lead não encontrado');
    }
};

window.contactLead = function(leadId) {
    const lead = window.marketingAutomization?.getLeadById(leadId);
    const message = `Olá! Sou da Logyc Contabilidade e gostaria de conversar sobre nossos serviços.`;
    const whatsappUrl = `https://wa.me/5541987427111?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
};

window.exportLeads = function() {
    window.adminDashboard.exportLeads();
};

window.exportAllData = function() {
    window.adminDashboard.exportAllData();
};

window.clearAllData = function() {
    if (confirm('Tem certeza que deseja limpar todos os dados? Esta ação não pode ser desfeita.')) {
        localStorage.clear();
        location.reload();
    }
};

window.createNewTest = function() {
    alert('Funcionalidade de criação de novos testes A/B será implementada em breve!');
};

window.saveSettings = function() {
    alert('Configurações salvas com sucesso!');
};

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    window.adminDashboard = new AdminDashboard();
});

// Export for global use
window.AdminDashboard = AdminDashboard;
