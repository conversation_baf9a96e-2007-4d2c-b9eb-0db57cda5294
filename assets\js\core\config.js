/**
 * Logyc Contabilidade - Configurações Principais
 * Arquivo de configuração central do projeto
 */

// Configurações da empresa
const COMPANY_CONFIG = {
    name: 'Logyc Contabilidade',
    slogan: 'Seu Sucesso é Nossa Prioridade',
    phone: '+*************',
    phoneFormatted: '+55 (41) 98742-7111',
    email: '<EMAIL>',
    website: 'https://logyccontabilidade.com.br',
    address: {
        city: 'Curitiba',
        state: 'PR',
        country: 'Brasil'
    },
    coverage: 'Atendemos todo o Brasil',

    // Redes sociais
    social: {
        whatsapp: 'https://wa.me/*************',
        facebook: 'https://www.facebook.com/logyccontabilidade',
        instagram: 'https://www.instagram.com/logyccontabilidade',
        linkedin: 'https://www.linkedin.com/company/logyccontabilidade'
    },

    // Configurações de negócio
    business: {
        workingHours: '08:00 - 18:00',
        workingDays: 'Segunda a Sexta',
        responseTime: '24 horas',
        coverage: 'Todo o Brasil'
    },

    // WhatsApp
    whatsapp: {
        number: '*************',
        baseUrl: 'https://wa.me/',
        messages: {
            default: 'Olá! Gostaria de saber mais sobre os serviços da Logyc Contabilidade.',
            calculator: 'Olá! Usei a calculadora no site e gostaria de saber mais sobre os serviços da Logyc Contabilidade.',
            switchAccounting: 'Olá! Gostaria de trocar minha contabilidade para a Logyc Contabilidade.',
            help: 'Olá! Preciso de ajuda com serviços contábeis.',
            contact: 'Olá! Entrei em contato através do site da Logyc Contabilidade.'
        }
    }
};

// Configurações técnicas
const TECH_CONFIG = {
    // Analytics
    analytics: {
        googleAnalyticsId: 'GA_MEASUREMENT_ID', // Substituir pelo ID real
        facebookPixelId: 'FB_PIXEL_ID', // Substituir pelo ID real
        hotjarId: 'HOTJAR_ID' // Substituir pelo ID real
    },
    
    // APIs
    apis: {
        baseUrl: window.location.origin,
        endpoints: {
            contact: '/api/contact',
            newsletter: '/api/newsletter',
            calculator: '/api/calculator'
        }
    },
    
    // PWA
    pwa: {
        cacheName: 'logyc-v1',
        offlineUrl: '/offline.html'
    },
    
    // Performance
    performance: {
        lazyLoadOffset: 100,
        debounceDelay: 300,
        throttleDelay: 100
    }
};

// Configurações de UI
const UI_CONFIG = {
    // Animações
    animations: {
        duration: 300,
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
        stagger: 100
    },
    
    // Breakpoints
    breakpoints: {
        mobile: 768,
        tablet: 1024,
        desktop: 1200
    },
    
    // Cores (sincronizado com CSS)
    colors: {
        primary: '#005aec',
        secondary: '#01d800',
        accent: '#ffe206',
        danger: '#fd0e35'
    }
};

// Exportar configurações
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { COMPANY_CONFIG, TECH_CONFIG, UI_CONFIG };
} else {
    window.COMPANY_CONFIG = COMPANY_CONFIG;
    window.TECH_CONFIG = TECH_CONFIG;
    window.UI_CONFIG = UI_CONFIG;
}
